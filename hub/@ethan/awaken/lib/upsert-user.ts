"use server";

import { createClient } from "../db/client.ts";
import { PLANS } from "./plans.ts";
import { encryptProfile, setProfileText } from "../action.ts";
import { User } from "../db/types.ts";

const db = createClient();

/**
 * Initialize a new user with minimal profile and coach settings
 * This replaces the onboarding flow initialization
 */
const initializeNewUser = async (user: User) => {
  try {
    // Create minimal profile text
    const profileText = `
    Name: ${user.name || 'Unknown'}
    User ID: ${user.channelId}

    Baseline State of Being: To be discovered
    Level of Self-Awareness: To be discovered
    Openness to Change: To be discovered
    Preferred Learning Style: To be discovered
    Spirituality/Faith: Not specified
    Personal Details: Not specified
    Immediate Goals: To be discovered through conversation
    Deeper Goals: To be discovered through conversation
    `;

    // Set profile text
    await setProfileText(user.channelId.toString(), profileText);

    // Initialize coach settings and meta feedback
    const metaFeedback = `- Keep it simple: This is a new client. You don't know their reality and what they mean yet. Slow down, listen, ask ONE simple question, like 'Go on', or 'What's important about that?', 'Say more', 'I see, what do you mean by X?'. Explore and understand their current experience first. (KOKOROS_CORE_PRINCIPLES #8 - SLOW DOWN).

- Get specific: Yes, you coach the being not the problem. BUT the client's story of the problem will reveal their being. Instead of generalities, guide them to get specific. Ask a simple SINGLE question to explore concrete examples of how they are already living this and what it means to them (KOKOROS_CORE_PRINCIPLES #4 - Surface the obstacle BEFORE transcending it) 

- Listen: After posing a potent inquiry, resist the urge to layer on additional questions or prompts. Trust the transformative power of ONE question and allow the client to marinate in it. Offer them as much space as they need to process and respond (KOKOROS_CORE_PRINCIPLES #3 - BREVITY is power)
  `;

    // Update user with coach settings
    await db
      .updateTable("user")
      .set({
        selectedCoach: "Kokoro",
        coachAccess: JSON.stringify({ "Kokoro": true, "JP AI": true }),
        profileInitiated: false,
        messagesTillProfileUpdate: 15,
        metaFeedback,
        messagesTillFeedback: 4,
      })
      .where("channelId", "=", user.channelId)
      .execute();

    // Initialize coach attributes for default coach
    await db
      .insertInto("user_coach_attributes")
      .values({
        channel_id: user.channelId,
        coach_name: "Kokoro",
        data_sharing_enabled: 0, // SQLite boolean as integer
        coach_notes: "",
        meta_feedback: "", // Start with empty metafeedback
        messages_till_feedback: 4
      })
      .execute();

    console.log(`[INIT] Successfully initialized new user ${user.channelId} with coach attributes`);
  } catch (error) {
    console.error(`[INIT] Error initializing user ${user.channelId}:`, error);
    // Don't throw - we don't want to fail user creation if initialization fails
  }
};

export const upsertUser = async (user: {
  email: string;
  name: string;
  image?: string;
}) => {
  const createdAt = new Date().toISOString();

  const channelId = Math.floor(100000000 + Math.random() * 900000000);

  const data = await db
    .insertInto("user")
    .values({
      name: user.name,
      email: user.email,
      image: user.image,
      channelId,
      createdAt,
      onboarding: true,
      firstMessage: true,
      monthlyEssenceBalance: 15
    })
    .onConflict((oc) =>
      oc.doUpdateSet((e) => ({
        email: user.email,
        name: user.name,
        image: user.image,
      }))
    )
    .returningAll()
    .executeTakeFirstOrThrow();
  const isNew = createdAt === data.createdAt;

  console.log("isNew ", isNew);

  // Initialize new users with minimal profile
  if (isNew) {
    await initializeNewUser(data);
  }

  return { ...data, isNew };
};


export const getUser = (email: string) =>
  db
    .selectFrom("user")
    .selectAll()
    .where("email", "=", email)
    .executeTakeFirst();