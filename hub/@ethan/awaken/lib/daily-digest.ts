"use server";
import Reframe from "@";
import { db } from "./db.ts";
import { v4 as uuid } from "npm:uuid";
import CryptoJS from "npm:crypto-js";

// Server-side helper versions (cannot import client module)
const AI_ONLY_COACHES = ["Kokoro", "Grace", "Nexus"] as const;

const isAIOnlyCoach = (coachName: string): boolean =>
  AI_ONLY_COACHES.includes(coachName as (typeof AI_ONLY_COACHES)[number]);

const getHumanCoachName = (coachName: string): string =>
  coachName.endsWith(" AI") ? coachName.slice(0, -3) : coachName;

// Build the digest heading using these helpers
const getCoachDigestHeading = (coachName: string): string =>
  isAIOnlyCoach(coachName)
    ? `Awaken's creators, in '${coachName}' channel`
    : `The actual ${getHumanCoachName(coachName)}, in '${coachName}' channel`;

const secretKey = Reframe.env.SECRET_KEY;

export interface UnreadMessage {
  id: string;
  content: string;
  date: string;
  coachName: string;
  messageType: string;
}

export interface CoachDigest {
  coachName: string;
  messageCount: number;
  messages: UnreadMessage[];
}

export interface UserDigest {
  userId: string;
  email: string;
  firstName: string;
  timezone: string;
  totalMessageCount: number;
  coachCount: number;
  coaches: CoachDigest[];
}

/**
 * Get unread messages for a user from the last 24 hours, grouped by coach
 * 
 * @param channelId - The user's channel ID
 * @param windowStart - Start of the 24-hour window (ISO string)
 * @param windowEnd - End of the 24-hour window (ISO string)
 * @returns UserDigest object with unread messages grouped by coach
 */
export const getUnreadMessagesForDigest = async (
  channelId: string | number,
  windowStart: string,
  windowEnd: string
): Promise<UserDigest | null> => {
  try {
    console.log(`[DIGEST] Getting unread messages for channel ${channelId} between ${windowStart} and ${windowEnd}`);
    
    // First get user info
    const user = await db
      .selectFrom("user")
      .select(["email", "name", "timezone"])
      .where("channelId", "=", Number(channelId))
      .executeTakeFirst();
    
    if (!user) {
      console.log(`[DIGEST] No user found for channel ${channelId}`);
      return null;
    }
    
    // Get unread messages from coaches within the time window
    const messages = await db
      .selectFrom("conversation")
      .select([
        "id",
        "content", 
        "date",
        "coachName",
        "messageType",
        "sender"
      ])
      .where("channelId", "=", Number(channelId))
      .where("sender", "in", ["assistant", "coach"])
      .where("messageType", "in", ["message", "coach_message"]) // Exclude coach_message and daily_awakening
      .where((eb) => eb.or([
        eb("seenByUser", "=", 0),
        eb("seenByUser", "is", null),
      ]))
      .where("date", ">=", windowStart)
      .where("date", "<=", windowEnd)
      .orderBy("date", "asc")
      .execute();
    
    console.log(`[DIGEST] Found ${messages.length} unread messages for channel ${channelId}`);
    
    if (messages.length === 0) {
      return null;
    }
    
    // Decrypt messages and group by coach
    const coachGroups: Record<string, CoachDigest> = {};
    
    for (const message of messages) {
      try {
        // Decrypt the message content
        const decryptedContent = CryptoJS.AES.decrypt(message.content, secretKey).toString(CryptoJS.enc.Utf8);
        // Determine display label based on sender/type
        const label = message.sender === "coach"
          ? getCoachDigestHeading(message.coachName)
          : message.coachName;

        if (!coachGroups[label]) {
          coachGroups[label] = {
            coachName: label,
            messageCount: 0,
            messages: []
          };
        }
        
        coachGroups[label].messages.push({
          id: message.id,
          content: decryptedContent,
          date: message.date,
          coachName: message.coachName,
          messageType: message.messageType
        });
        
        coachGroups[label].messageCount++;
      } catch (error) {
        console.error(`[DIGEST] Error decrypting message ${message.id}:`, error);
        // Skip this message if decryption fails
      }
    }
    
    const coaches = Object.values(coachGroups);
    const totalMessageCount = coaches.reduce((sum, coach) => sum + coach.messageCount, 0);
    
    // Extract first name from full name
    const firstName = user.name?.split(' ')[0] || 'there';
    
    return {
      userId: channelId.toString(),
      email: user.email,
      firstName,
      timezone: user.timezone || 'Europe/London',
      totalMessageCount,
      coachCount: coaches.length,
      coaches
    };
    
  } catch (error) {
    console.error(`[DIGEST] Error getting unread messages for channel ${channelId}:`, error);
    return null;
  }
};

/**
 * Check if a daily digest has already been sent for a user on a specific date
 * 
 * @param userId - The user's channel ID
 * @param digestDate - The date in YYYY-MM-DD format
 * @returns true if digest was already sent, false otherwise
 */
export const hasDigestBeenSent = async (
  userId: string,
  digestDate: string
): Promise<boolean> => {
  try {
    const existingDigest = await db
      .selectFrom("dailyDigestJob")
      .select(["id"])
      .where("userId", "=", userId)
      .where("digestDate", "=", digestDate)
      .where("sentAt", "is not", null)
      .executeTakeFirst();
    
    return !!existingDigest;
  } catch (error) {
    console.error(`[DIGEST] Error checking if digest was sent for user ${userId} on ${digestDate}:`, error);
    return false;
  }
};

/**
 * Record that a daily digest has been sent
 * 
 * @param userId - The user's channel ID
 * @param digestDate - The date in YYYY-MM-DD format
 * @param messageCount - Number of messages in the digest
 * @param coachCount - Number of coaches in the digest
 */
export const recordDigestSent = async (
  userId: string,
  digestDate: string,
  messageCount: number,
  coachCount: number
): Promise<void> => {
  try {
    const digestId = uuid();
    const now = new Date().toISOString();
    
    await db
      .insertInto("dailyDigestJob")
      .values({
        id: digestId,
        userId,
        digestDate,
        sentAt: now,
        messageCount,
        coachCount,
        createdAt: now
      })
      .execute();
    
    console.log(`[DIGEST] Recorded digest sent for user ${userId} on ${digestDate}: ${messageCount} messages from ${coachCount} coaches`);
  } catch (error) {
    console.error(`[DIGEST] Error recording digest sent for user ${userId} on ${digestDate}:`, error);
    throw error;
  }
};

/**
 * Get all users who have daily email enabled and their timezone info
 * 
 * @returns Array of users with email preferences and timezone
 */
export const getUsersForDigest = async (): Promise<Array<{
  channelId: number;
  email: string;
  name: string;
  timezone: string;
  emailDaily: number;
}>> => {
  try {
    const users = await db
      .selectFrom("user")
      .select(["channelId", "email", "name", "timezone", "emailDaily"])
      .where("emailDaily", "=", 1) // Only users who have daily email enabled
      .execute();
    
    console.log(`[DIGEST] Found ${users.length} users with daily email enabled`);
    return users;
  } catch (error) {
    console.error("[DIGEST] Error getting users for digest:", error);
    return [];
  }
};

/**
 * Clean up old digest records (older than 30 days)
 */
export const cleanupOldDigests = async (): Promise<void> => {
  try {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const cutoffDate = thirtyDaysAgo.toISOString().split('T')[0]; // YYYY-MM-DD format

    const result = await db
      .deleteFrom("dailyDigestJob")
      .where("digestDate", "<", cutoffDate)
      .execute();

    console.log(`[DIGEST] Cleaned up ${result.length} old digest records older than ${cutoffDate}`);
  } catch (error) {
    console.error("[DIGEST] Error cleaning up old digests:", error);
  }
};

/**
 * Generate the email subject line for the daily digest
 *
 * @param userDigest - The user digest data
 * @returns Email subject string
 */
export const generateDigestSubject = (userDigest: UserDigest): string => {
  const { totalMessageCount, coaches } = userDigest;
  const messageText = totalMessageCount === 1 ? "message" : "messages";

  if (coaches.length === 1) {
    return `You have ${totalMessageCount} new ${messageText} from ${coaches[0].coachName}`;
  } else {
    const firstCoach = coaches[0].coachName;
    return `You have ${totalMessageCount} new ${messageText} from ${firstCoach} and others`;
  }
};

/**
 * Generate the plain text email body for the daily digest
 *
 * @param userDigest - The user digest data
 * @param localDate - The local date string for the user's timezone
 * @returns Email body string
 */
export const generateDigestEmailBody = (
  userDigest: UserDigest,
  localDate: string
): string => {
  const { firstName, coaches, totalMessageCount, coachCount } = userDigest;

  // Friendly introductory paragraph
  let emailBody = `Hi ${firstName},\n\n`;
  emailBody += `You have **${totalMessageCount} new message${
    totalMessageCount === 1 ? "" : "s"
  }** from ${coachCount} coach${coachCount === 1 ? "" : "es"} on **${localDate}**.\n\n`;
  emailBody += `Here’s a quick summary:\n\n`;

  // Build digest per-coach
  for (const coach of coaches) {
    emailBody += `--- \n\n`;
    emailBody += `**${coach.coachName}**  (${coach.messageCount} new)\n`;

    for (const message of coach.messages) {
      const messageDate = new Date(message.date);
      const localTime = messageDate.toLocaleTimeString("en-US", {
        hour: "numeric",
        minute: "2-digit",
        hour12: true,
        timeZone: userDigest.timezone,
      });

      const truncatedContent =
        message.content.length > 80
          ? `${message.content.substring(0, 77)}…`
          : message.content;

      emailBody += `- ${localTime} — “${truncatedContent}”\n`;
    }

    emailBody += `\n`;
  }

  emailBody += `--- \n\n`;
  emailBody += `Reply any time to keep the conversation going.\n`;
  emailBody += `\n`;
  emailBody += `To awakening 🌱`;

  return emailBody;
};
