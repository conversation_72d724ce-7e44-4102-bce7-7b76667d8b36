"use client";

import React, { useEffect } from "npm:react@canary";
import { Button, Text } from "@reframe/ui/main.tsx";
import { motion } from "npm:framer-motion";
import { CircleXIcon } from "./icons.tsx";
import { EssenceIcon } from "./icons.tsx";
import { createTopUpSession } from "../actions/stripe-actions.ts";
import { isExpoIOS } from "./platform-utils.ts";

export const TOP_UP_OPTIONS = [
  { price: 200, essence: 1100, popular: true, id: "tu_xyh9uiondi4" },
  { price: 50, essence: 230, popular: false, id: "tu_xnui32ho58u" },
  { price: 25, essence: 100, popular: false, id: "tu_xyh9uiondi4" },
  { price: 10, essence: 38, popular: false, id: "tu_xnui32ho58u" },
  { price: 5, essence: 18, popular: false, id: "tu_xsainm1928u" },
];
interface TopUpOverlayProps {
  isOpen: boolean;
  onClose: () => void;
  channelId: string;
}

export const TopUpOverlay: React.FC<TopUpOverlayProps> = ({ isOpen, onClose, channelId }) => {
  const [loading, setLoading] = React.useState<number | null>(null);
  const [error, setError] = React.useState<string | null>(null);

  const isIosExpo = isExpoIOS();

  // Add ESC key handler for accessibility
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };
    
    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [onClose]);

  // Lock body scroll when overlay is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = '';
      };
    }
  }, [isOpen]);

  const handleTopUp = async (price: number, essence: number, topUpId: string) => {
    // Block payment for iOS Expo
    if (isIosExpo) {
      return;
    }

    try {
      setLoading(price);
      setError(null);

      const checkoutUrl = await createTopUpSession(channelId, price, essence, topUpId);
      if (checkoutUrl) {
        globalThis.location.href = checkoutUrl;
      } else {
        throw new Error("Failed to create checkout session");
      }
    } catch (err) {
      console.error("Top-up error:", err);
      setError(err instanceof Error ? err.message : "Failed to process top-up");
    } finally {
      setLoading(null);
    }
  };

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50"
      role="dialog"
      aria-modal="true"
      aria-label="Top Up Essence"
    >
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 10 }}
        transition={{ type: "spring", damping: 25, stiffness: 300 }}
        className="fixed inset-0 flex flex-col bg-[#1a1a1a]"
      >
        {/* Header with close button in natural flow */}
        <div className="flex justify-end p-4 sm:p-6" style={{ paddingTop: 'env(safe-area-inset-top, 1rem)' }}>
          <button
            onClick={onClose}
            className="p-2 rounded-full hover:bg-white/10 transition-colors"
            aria-label="Close top-up"
          >
            <CircleXIcon size={24} color="#ffffff" />
          </button>
        </div>

        {/* Scrollable content area */}
        <div 
          className="flex-1 overflow-y-auto overscroll-contain px-4 sm:px-8 pb-8"
          style={{ 
            WebkitOverflowScrolling: 'touch',
            paddingBottom: 'env(safe-area-inset-bottom, 2rem)'
          }}
        >
          <div className="w-full max-w-md mx-auto">
            {/* Title Section */}
            <div className="text-center mb-8">
              <div className="flex justify-center mb-4">
                <div className="text-[#ff6b35]">
                  <EssenceIcon size={48} color="#ff6b35" />
                </div>
              </div>
              <div className="space-y-2">
                <Text css="text-2xl font-bold text-white block">Top Up Essence</Text>
                <Text css="text-gray-400 block">
                  Power your transformative journey
                </Text>
              </div>
            </div>

            {/* iOS Expo Warning */}
            {isIosExpo && (
              <div className="mb-6 p-4 bg-[#FCA311]/20 border border-[#FCA311]/30 rounded-lg text-center">
                <Text css="text-[#FCA311] font-medium block">
                  Purchasing essence isn't supported in this app. We know this isn't ideal.
                </Text>
              </div>
            )}

            {/* Options */}
            <div className="space-y-4">
            {TOP_UP_OPTIONS.map(({ essence, price, popular, id }) => (
              <motion.div
                key={essence}
                whileHover={!isIosExpo ? { scale: 1.02 } : {}}
                className={`
                  relative p-4 rounded-lg border ${isIosExpo ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}
                  ${popular 
                    ? 'border-[#FCA311] bg-gradient-to-b from-[#FCA31115] to-transparent' 
                    : 'border-gray-700 hover:border-[#FCA311]/50'}
                `}
                onClick={() => !isIosExpo && handleTopUp(price, essence, id)}
              >
                {popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-[#FCA311] text-black text-xs font-bold px-3 py-1 rounded-full">
                      MOST POPULAR
                    </span>
                  </div>
                )}

                <div className="flex justify-between items-start">
                  <div className="space-y-1 flex flex-col">
                    <Text css="text-xl font-bold text-white block">{essence} Essence</Text>
                    <Text css="text-sm text-gray-400 block"></Text>
                  </div>
                  {!isIosExpo && (
                    <div className="text-right flex flex-col">
                      <Text css="text-2xl font-bold text-[#FCA311] block">${price}</Text>
                      <Text css="text-sm text-gray-400 block">${(price/essence * 100).toFixed(2)}/100 essence</Text>
                    </div>
                  )}
                </div>

                {loading === price && !isIosExpo && (
                  <div className="absolute inset-0 bg-black/50 rounded-lg flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-2 border-[#FCA311] border-t-transparent" />
                  </div>
                )}
              </motion.div>
            ))}
          </div>

            {error && (
              <div className="mt-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-500 text-sm text-center">
                {error}
              </div>
            )}
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
}; 