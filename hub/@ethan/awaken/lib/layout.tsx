"use client";

import React, { useState, useEffect } from "npm:react@canary";
import { Button } from "@reframe/ui/main.tsx";
import { NavDrawer } from "./nav-drawer.tsx";
import { ProfilePage } from "./profile/profile.tsx";
import { TopUpOverlay } from "./top-up-overlay.tsx";
import { PaymentOverlay } from "./profile/payment-overlay.tsx";
import { SuccessToast } from "./success-toast.tsx";
import { AnimatePresence, motion } from "npm:framer-motion";
import { animated, useSpring } from "npm:@react-spring/web";
import { MenuIcon } from "@reframe/icons/icons/MenuIcon.ts";

interface UserData {
  subscription?: {
    planId: string;
    essenceBalance?: number;
    id?: string;
    currentPeriodEnd?: string;
  };
  emailDaily?: boolean;
  callMinsThisPeriod?: number;
  totalCallMins?: number;
  messagesThisPeriod?: number;
  totalMessages?: number;
  [key: string]: any; // For any other properties
}

interface LayoutProps {
  user: {
    channelId?: string;
    [key: string]: any; // For any other properties
  };
  children: React.ReactNode;
  userData: UserData | null;
  _onUpgrade?: () => void; // Prefixed with underscore as it's not used directly
  enrollmentMessage?: string | null;
  isLoadingEnrollment?: boolean;
  currentPath?: string; // Current path for highlighting active nav item
  keepDrawer?: boolean;
}

export function Layout({
  user,
  children,
  userData,
  _onUpgrade,
  enrollmentMessage,
  isLoadingEnrollment = false,
  currentPath = '',
  keepDrawer = true
}: LayoutProps) {
  // State for nav drawer
  const [isNavOpen, setIsNavOpen] = useState(false);
  
  // State for profile
  const [showProfile, setShowProfile] = useState(false);
  
  // State for payment modal
  const [isPaymentOpen, setIsPaymentOpen] = useState(false);
  
  // State for top-up modal
  const [isTopUpOpen, setIsTopUpOpen] = useState(false);
  
  // Success toast state
  const [topUpSuccessToast, setTopUpSuccessToast] = useState({
    visible: false,
    message: ""
  });

  // Handlers
  const handleOpenNav = () => {
    setIsNavOpen(true);
  };

  const handleCloseNav = () => {
    setIsNavOpen(false);
  };

  const handleProfileClick = () => {
    setIsNavOpen(false);
    setShowProfile(true);
  };

  const handleUpgradeClick = () => {
    setIsNavOpen(false);
    setIsPaymentOpen(true);
  };

  const handleSignOut = () => {
    // Navigate to sign out route
    if (typeof window !== 'undefined') {
      window.location.href = "/auth/sign-out";
    }
  };

  // Show top-up success toast
  const showTopUpSuccessToast = (message: string) => {
    setTopUpSuccessToast({
      visible: true,
      message
    });
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
      setTopUpSuccessToast({
        visible: false,
        message: ""
      });
    }, 5000);
  };

  // Check for top-up success on page load
  React.useEffect(() => {
    // Check URL hash for top-up success - only in browser environment
    if (typeof window !== 'undefined' && window.location.hash === '#top-up-success') {
      // Remove the hash from the URL without reloading the page
      window.history.replaceState(null, '', window.location.pathname);
      
      // Show success toast
      showTopUpSuccessToast("Essence added successfully!");
    }
  }, []);

  // Prevent body/html scrolling to ensure only internal components scroll
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      // Set overflow hidden on body and html to prevent page-level scrolling
      document.body.style.overflow = 'hidden';
      document.documentElement.style.overflow = 'hidden';
      
      // Cleanup function to restore scrolling when component unmounts
      return () => {
        document.body.style.overflow = '';
        document.documentElement.style.overflow = '';
      };
    }
  }, []);

  // Animation variants for page transitions
  const pageVariants = {
    initial: {
      opacity: 0,
      x: 20,
    },
    animate: {
      opacity: 1,
      x: 0,
      transition: {
        type: "spring",
        stiffness: 260,
        damping: 20,
      }
    },
    exit: {
      opacity: 0,
      x: -20,
      transition: {
        duration: 0.3
      }
    }
  };

  return (
    <div className="min-h-svh max-h-svh flex w-full flex-col bg-transparent text-white overflow-hidden">
      {/* Main Content */}
      <div className="relative flex-1 flex flex-col overflow-hidden">
        {/* Upper circle */}
        <animated.div
          className="absolute w-[506px] h-[506px] rounded-full blur-[100px] pointer-events-none"
          style={{
            top: "-180px",
            left: "50%",
            transform: "translateX(-50%)",
            background: "linear-gradient(270deg, #FF5727 43.87%, #FFC360 66.94%)",
            opacity: 0.6,
          }}
        />
        {/* Fixed Header - always visible */}
        <header className="fixed top-0 left-0 right-0 h-[60px] bg-transparent z-30 flex items-center justify-between px-4 pointer-events-none">
          {keepDrawer && (
            <div className="absolute top-4 left-7 z-20 pointer-events-auto">
              <Button
                variant="ghost"
                onClick={handleOpenNav} // Open the menu
                css="p-0 w-8 h-8 flex items-center justify-center text-white hover:bg-transparent active:bg-transparent rounded-md"
                style={{ WebkitTapHighlightColor: 'transparent' }}
              >
                <MenuIcon className="w-7 h-7" /> {/* Ensure MenuIcon is imported and correct size */}
              </Button>
            </div>
          )}
        </header>

        {/* Lower circle */}
        <animated.div
          className="fixed bottom-0 left-1/2 -translate-x-1/2 w-[520px] h-[420px] md:w-[706px] md:h-[506px] rounded-full blur-[100px] pointer-events-none"
          style={{
            bottom: -340,
            background: "linear-gradient(270deg, #FF5727 43.87%, #FFC360 66.94%)",
            opacity: 0.3,
          }}
        />
        {/* Content below header without padding so header overlays */}
        <div className="flex-1 flex flex-col w-full overflow-hidden">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentPath || 'default-key'}
              className="flex-1 flex flex-col w-full overflow-hidden"
              initial="initial"
              animate="animate"
              exit="exit"
              variants={pageVariants}
            >
              {children}
            </motion.div>
          </AnimatePresence>
        </div>
        
        
        {keepDrawer && (
        <NavDrawer
          user={user}
          isSubscribed={userData?.subscription?.planId?.startsWith('premium_')}
          isOpen={isNavOpen}
          onProfileClick={handleProfileClick}
          onUpgradeClick={handleUpgradeClick}
          onSignOut={handleSignOut}
          onClose={handleCloseNav}
          currentPath={currentPath}
          essenceBalance={
            (userData?.monthlyEssenceBalance || 0) + (userData?.addedEssenceBalance || 0)
          }
        />
        )}
        
        
        {/* Profile Modal */}
        {showProfile && (
          <ProfilePage
            user={user}
            userData={userData}
            onClose={() => setShowProfile(false)}
            setIsTopUpOpen={setIsTopUpOpen}
            onUpgrade={() => {
              setShowProfile(false);
              setIsPaymentOpen(true);
            }}
          />
        )}
        
        {/* Payment Modal */}
        <PaymentOverlay 
          isOpen={isPaymentOpen} 
          onClose={() => setIsPaymentOpen(false)}
          user={user}
          userData={userData}
          enrollmentMessage={enrollmentMessage}
          isLoadingEnrollment={isLoadingEnrollment}
        />
        
        {/* Top-up Modal */}
        <TopUpOverlay
          isOpen={isTopUpOpen}
          onClose={() => setIsTopUpOpen(false)}
          channelId={user.channelId?.toString() || ''}
        />
        
        {/* Success Toast */}
        <AnimatePresence>
          {topUpSuccessToast.visible && (
            <SuccessToast 
              message={topUpSuccessToast.message} 
              onClose={() => setTopUpSuccessToast({ ...topUpSuccessToast, visible: false })} 
            />
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
