"use client";

import React, { useEffect, useState, useRef } from "npm:react@canary";
import { Button, Text, X, Y } from "@reframe/ui/main.tsx";
import { motion, AnimatePresence } from "npm:framer-motion";
import { Logo } from "../logo.tsx";
import {
  SparklesIcon,
  LogOutIcon,
  MessageIcon,
  UserIcon,
  CrownIcon,
  CalendarIcon,
  PhoneIcon,
  StarIcon,
  SmallBellIcon,
  EssenceIcon,
  CircleXIcon
} from "../icons.tsx";
import { PlanType, PLANS } from "../plans.ts";
import { updateDailyEmailSetting } from "../../actions/db/user-actions.ts";
import { getUserData } from "../db.ts";
import { TopUpOverlay } from "../top-up-overlay.tsx";
import { CancelSubscriptionModal } from "./cancel-subscription-modal.tsx";

// Cache for userData data by channelId
const userDataCache = new Map();

const nameMapping = {
  free: "Awaken Balance (Free)",
  basic: "Awaken Beyond",
  premium: "Awaken Boundless"
};

// Add this helper function at the top level
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

export const ProfilePage = ({
  user,
  userData: initialUserData,
  onClose,
  onUpgrade,
  setIsTopUpOpen
}: {
  user: any;
  userData: any;
  onClose: () => void;
  onUpgrade: () => void;
  setIsTopUpOpen: (isOpen: boolean) => void;
}) => {
  // Use the cached data if available, otherwise use initialSubscription
  const cachedUserData = user?.channelId ? userDataCache.get(user.channelId) : null;
  const [userData, setUserData] = useState(cachedUserData || initialUserData);
  const [canCancelSubscription, setCanCancelSubscription] = useState(false);

  console.log("userData", userData, initialUserData);
  // Track if this is the first render
  const firstRenderRef = useRef(true);

  // Add state for daily email toggle - prefer localStorage first, then cached or initial subscription data
  const [isDailyEmail, setIsDailyEmail] = useState<boolean>(() => {
    // Check localStorage first for immediate value
    if (typeof window !== 'undefined') {
      const cachedEmailDaily = localStorage.getItem('emailDaily-' + user.channelId);
      if (cachedEmailDaily !== null) {
        return cachedEmailDaily === '1';
      }
    }

    // Fall back to subscription data
    return (cachedUserData?.emailDaily !== undefined) ? cachedUserData.emailDaily :
      (initialUserData?.emailDaily !== undefined) ? initialUserData.emailDaily :
        user.emailDaily === 1;
  });


  // Add state for top-up overlay

  // Add state for subscription cancellation modal
  const [showCancelModal, setShowCancelModal] = useState(false);

  // Fetch the latest subscription data when the component mounts
  useEffect(() => {
    const fetchLatestUserData = async () => {
      if (user?.channelId) {
        try {
          const userData = await getUserData(user.channelId.toString());
          if (userData) {
            setUserData(userData);

            // Update the email daily toggle state based on the subscription data
            if (userData.emailDaily !== undefined) {
              setIsDailyEmail(userData.emailDaily);
              // Store in localStorage for future reference
              localStorage.setItem('emailDaily-' + user.channelId, userData.emailDaily ? '1' : '0');
            }

            // Update the cache with the latest data
            userDataCache.set(user.channelId, userData);
          }
        } catch (error) {
          console.error('Failed to fetch subscription in profile:', error);
        }
      }
    };

    // Only fetch if this is the first time the component renders
    // This ensures it fetches whenever the profile is opened from chat.tsx
    if (firstRenderRef.current) {
      fetchLatestUserData();
      firstRenderRef.current = false;
    }
  }, [user?.channelId]);

  const isSubscribed = userData?.subscription?.planId?.startsWith('premium_');
  const subscriptionPlan = userData?.subscription?.planId?.split('_')[0] || "free";
  console.log("MONTHLY ESSENCE BALANCE", PLANS[subscriptionPlan as PlanType].essencePerMonth);
  
  useEffect(() => {
    console.log("isSubscribed", isSubscribed);
    console.log("userData", userData);
    if(isSubscribed && userData.currentPeriodEnd !== null && !userData.subscription.cancelAtPeriodEnd) {
      setCanCancelSubscription(true);
    }
  }, [isSubscribed, userData]);

  console.log("canCancelSubscription", canCancelSubscription);
                                

  // Add handler for daily email toggle
  const handleToggleDailyEmail = async () => {
    // Flip the local state first for immediate UI response
    const newValue = !isDailyEmail;
    setIsDailyEmail(newValue);

    // Store in localStorage immediately for smooth transitions
    localStorage.setItem('emailDaily-' + user.channelId, newValue ? '1' : '0');

    // Send to server route
    try {
      const success = await updateDailyEmailSetting(user.channelId, newValue);

      if (!success) {
        // Revert local state if request fails
        setIsDailyEmail(!newValue);
        // Also revert localStorage
        localStorage.setItem('emailDaily-' + user.channelId, (!newValue) ? '1' : '0');
        console.error("Failed to update daily email setting");
      }
    } catch (error) {
      // Revert local state if request fails
      setIsDailyEmail(!newValue);
      // Also revert localStorage
      localStorage.setItem('emailDaily-' + user.channelId, (!newValue) ? '1' : '0');
      console.error("Request error:", error);
    }
  };

  // Add handler for refreshing user data after cancellation
  const handleCancellationComplete = async () => {
    if (user?.channelId) {
      try {
        const freshUserData = await getUserData(user.channelId.toString());
        if (freshUserData) {
          setUserData(freshUserData);
          setCanCancelSubscription(false);
          // Update the cache with the latest data
          userDataCache.set(user.channelId, freshUserData);
        }
      } catch (error) {
        console.error('Failed to refresh user data after cancellation:', error);
      }
    }
  };

  // Add ESC key handler for accessibility
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };
    
    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [onClose]);

  // Lock body scroll when profile is open
  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = '';
    };
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50"
      role="dialog"
      aria-modal="true"
      aria-label="User Profile"
    >
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 10 }}
        transition={{ type: "spring", damping: 25, stiffness: 300 }}
        className="fixed inset-0 flex flex-col bg-[#1a1a1a]"
      >
        {/* Header with close button in natural flow */}
        <div className="flex justify-end p-4 sm:p-6" style={{ paddingTop: 'env(safe-area-inset-top, 1rem)' }}>
          <button
            onClick={onClose}
            className="p-2 rounded-full hover:bg-white/10 transition-colors"
            aria-label="Close profile"
          >
            <CircleXIcon size={24} color="#ffffff" />
          </button>
        </div>

        {/* Scrollable content area */}
        <div 
          className="flex-1 overflow-y-auto overscroll-contain px-4 sm:px-8 pb-8"
          style={{ 
            WebkitOverflowScrolling: 'touch',
            paddingBottom: 'env(safe-area-inset-bottom, 2rem)'
          }}
        >
          <div className="w-full max-w-2xl mx-auto space-y-6">

            {/* Profile Header - enhanced for full-screen */}
            <div className="flex justify-between items-start mb-8">
              <div className="flex-1">
                <h1 className="text-2xl sm:text-3xl font-bold text-white mb-2">{user.name}</h1>
                <p className="text-sm sm:text-base text-gray-400 mb-3">{user.email}</p>
                <div className="flex items-center space-x-2 text-gray-400 text-sm">
                  <CalendarIcon size={16} color="#FCA311" />
                  <span>Member since {formatDate(user.createdAt)}</span>
                </div>
              </div>
              
              <div className="w-20 h-20 sm:w-24 sm:h-24 rounded-full bg-gradient-to-r from-[#FCA311] to-[#E85D04] p-1">
                <img
                  src={user.image}
                  alt={user.name}
                  className="w-full h-full rounded-full border-2 border-black"
                />
              </div>
            </div>

            {/* Subscription Section - enhanced spacing */}
            <div className="space-y-6">
              <div className="flex items-center justify-between bg-[#2a2a2a] rounded-lg p-4 sm:p-5 border border-[#FCA31133]">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-[#FCA31120] rounded-lg">
                    <CrownIcon size={20} color="#FCA311" />
                  </div>
                  <div>
                    <Text css="text-lg font-bold text-[#FCA311] block">
                      {nameMapping[subscriptionPlan as keyof typeof nameMapping]}
                    </Text>
                    {userData.subscription.currentPeriodEnd && (
                      <Text css="text-sm text-gray-400 mt-1 block">
                    {userData.subscription.cancelAtPeriodEnd ? (
                      <span>Access until {formatDate(userData.subscription.currentPeriodEnd)}</span>
                    ) : (
                      <span>Renews {formatDate(userData.subscription.currentPeriodEnd)}</span>
                    )}
                  </Text>
                )}
              </div>
            </div>
          </div>

              {/* Essence Balance Section - enhanced */}
              <div className="bg-[#2a2a2a] rounded-lg p-4 sm:p-5 border border-[#FCA31133]">
                {/* Top section with Monthly and Added Essence */}
                <div className="flex justify-between mb-3">
                  {/* Monthly Essence */}
                  <div>
                    <Text css="text-sm text-gray-400 mb-1 block">Monthly Essence</Text>
                    <div className="flex items-baseline space-x-1">
                      <Text css="text-xl font-bold text-[#FCA311]">
                    {Math.floor(userData.monthlyEssenceBalance)}
                      </Text>
                      <Text css="text-sm text-gray-400">/ {PLANS[subscriptionPlan as PlanType].essencePerMonth}</Text>
                </div>
                  </div>
                  
                  {/* Added Essence */}
                  <div className="text-right flex flex-col">
                    <Text css="text-sm text-gray-400 mb-1 block">Added Essence</Text>
                    <Text css="text-xl font-bold text-[#FCA311] block">
                  {Math.floor(userData.addedEssenceBalance)}
                </Text>
              </div>
            </div>
            
                {/* Separator */}
                <div className="h-px bg-gray-700 my-3"></div>
            
                {/* Total Essence bottom section */}
                <div className="flex justify-between items-start pt-2">
                  <div className="flex flex-col">
                    <Text css="text-base font-bold text-white block">Total Essence</Text>
                    <Text css="text-sm text-gray-400 mt-1 block">Available for your journey</Text>
              </div>
                  <div className="text-right flex flex-col">
                    <div className="flex items-center justify-end gap-2">
                      <div className="text-[#ff6b35]">
                        <EssenceIcon size={24} color="#ff6b35" />
                      </div>
                      <Text css="text-2xl font-bold text-[#FCA311] block">
                    {Math.floor(userData.monthlyEssenceBalance + userData.addedEssenceBalance)}
                  </Text>
                </div>
                <Text css="text-xs text-gray-400 mt-0.5 block">
                </Text>
              </div>
            </div>
            
                {/* Top Up button for subscribed users */}
                {isSubscribed && (
                  <Button
                    variant="default"
                    onClick={() => setIsTopUpOpen(true)}
                    css="mt-4 bg-[#FCA311] text-white hover:bg-[#FCA311]/90 w-full py-2.5 flex items-center justify-center gap-2 text-base"
                  >
                    Top Up
                  </Button>
                )}
          </div>

              {/* Upgrade Button - moved here for iOS visibility */}
              {!isSubscribed && (
                <Button
                  onClick={onUpgrade}
                  css="w-full bg-[#FCA311] text-white hover:bg-[#FCA311]/90 py-3 text-base"
                >
                  Upgrade
                </Button>
              )}

              {/* Daily Email Toggle - enhanced */}
              <div className="flex items-center justify-between bg-[#2a2a2a] rounded-lg p-4 sm:p-5">
                <span className="text-white text-base font-medium">Daily Awakening Email</span>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={isDailyEmail}
                onChange={handleToggleDailyEmail}
                className="sr-only peer"
              />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[#FCA311]/20 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-[#FCA311]" />
            </label>
          </div>

              {/* Usage Stats - enhanced */}
              <div className="bg-[#2a2a2a] rounded-lg p-4 sm:p-5">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {/* Call Usage Stats */}
                  <div className="bg-[#222222] rounded-lg p-4">
                    <div className="flex items-center mb-3">
                      <div className="p-1.5 bg-[#FCA31120] rounded-lg mr-2">
                        <PhoneIcon size={16} color="#FCA311" />
                      </div>
                      <Text css="text-sm text-gray-300 font-medium">Call Usage</Text>
                </div>
                
                    <div className="flex flex-col space-y-2">
                      <div className="flex justify-between items-baseline">
                        <Text css="text-sm text-gray-400">This Month:</Text>
                        <Text css="text-base font-bold text-white">
                          {Math.floor(userData.callMinsThisPeriod)} <span className="text-sm font-normal text-gray-400">mins</span>
                    </Text>
                  </div>
                      <div className="flex justify-between items-baseline">
                        <Text css="text-sm text-gray-400">All Time:</Text>
                        <Text css="text-base font-bold text-white">
                          {Math.floor(userData.totalCallMins)} <span className="text-sm font-normal text-gray-400">mins</span>
                    </Text>
                  </div>
                </div>
              </div>
              
                  {/* Chat Usage Stats */}
                  <div className="bg-[#222222] rounded-lg p-4">
                    <div className="flex items-center mb-3">
                      <div className="p-1.5 bg-[#FCA31120] rounded-lg mr-2">
                        <MessageIcon size={16} color="#FCA311" />
                      </div>
                      <Text css="text-sm text-gray-300 font-medium">Chat Usage</Text>
                </div>
                
                    <div className="flex flex-col space-y-2">
                      <div className="flex justify-between items-baseline">
                        <Text css="text-sm text-gray-400">This Month:</Text>
                        <Text css="text-base font-bold text-white">
                          {userData.messagesThisPeriod} <span className="text-sm font-normal text-gray-400">msgs</span>
                    </Text>
                  </div>
                      <div className="flex justify-between items-baseline">
                        <Text css="text-sm text-gray-400">All Time:</Text>
                        <Text css="text-base font-bold text-white">
                          {userData.totalMessages} <span className="text-sm font-normal text-gray-400">msgs</span>
                    </Text>
                  </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Cancel Link - enhanced */}
            {canCancelSubscription && (
              <div className="mt-6 text-center">
                <Button 
                  variant="link"
                  onClick={() => setShowCancelModal(true)}
                  css="text-sm text-white hover:text-red-300 hover:underline transition-colors px-3 py-2"
                >
                  Cancel Subscription
                </Button>
              </div>
            )}
        
            {/* Subscription Cancellation Modal */}
            {showCancelModal && (
              <CancelSubscriptionModal 
                isOpen={showCancelModal}
                onClose={() => setShowCancelModal(false)}
                subscriptionId={userData.subscription.id}
                currentPeriodEnd={userData.subscription.currentPeriodEnd}
                onCancellationComplete={handleCancellationComplete}
              />
            )}
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};
