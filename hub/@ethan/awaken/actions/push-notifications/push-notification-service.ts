"use server";

import { getActivePushTokens } from "./push-token-actions.ts";

export interface PushNotificationPayload {
  to: string;
  title: string;
  body: string;
  data?: Record<string, any>;
  sound?: string;
  badge?: number;
  priority?: 'default' | 'normal' | 'high';
  channelId?: string;
}

export interface CoachMessageNotificationData {
  channelId: string | number;
  coachName: string;
  messageContent: string;
  messageId?: string;
}

/**
 * Send push notification using Expo's push notification service
 */
export const sendExpoPushNotification = async (
  payload: PushNotificationPayload
): Promise<{ success: boolean; error?: string; receipt?: any }> => {
  try {
    console.log(`[PUSH_NOTIFICATION] Sending notification to ${payload.to}`);
    
    const response = await fetch('https://exp.host/--/api/v2/push/send', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Accept-encoding': 'gzip, deflate',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to: payload.to,
        title: payload.title,
        body: payload.body,
        data: payload.data || {},
        sound: payload.sound || 'default',
        badge: payload.badge,
        priority: payload.priority || 'high',
        channelId: payload.channelId || 'default',
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[PUSH_NOTIFICATION] Failed to send notification: ${response.status} ${errorText}`);
      return { success: false, error: `HTTP ${response.status}: ${errorText}` };
    }

    const result = await response.json();
    console.log(`[PUSH_NOTIFICATION] Notification sent successfully:`, result);
    
    return { success: true, receipt: result };
  } catch (error) {
    console.error("[PUSH_NOTIFICATION] Error sending push notification:", error);
    return { success: false, error: error.message };
  }
};

/**
 * Send push notification to all active devices for a user when they receive a coach message
 */
export const sendCoachMessageNotification = async (
  data: CoachMessageNotificationData
): Promise<{ success: boolean; sentCount: number; errors: string[] }> => {
  try {
    console.log(`[COACH_MESSAGE_NOTIFICATION] Sending notification for coach message from ${data.coachName} to channel ${data.channelId}`);
    
    // Get all active push tokens for the user
    const tokens = await getActivePushTokens(data.channelId);
    
    if (tokens.length === 0) {
      console.log(`[COACH_MESSAGE_NOTIFICATION] No active push tokens found for channel ${data.channelId}`);
      return { success: true, sentCount: 0, errors: [] };
    }

    console.log(`[COACH_MESSAGE_NOTIFICATION] Found ${tokens.length} active tokens for channel ${data.channelId}`);

    // Prepare notification payload
    const title = `Message from ${data.coachName}`;
    const body = data.messageContent.length > 100 
      ? `${data.messageContent.substring(0, 97)}...`
      : data.messageContent;

    const notificationData = {
      type: 'coach_message',
      coachName: data.coachName,
      channelId: data.channelId.toString(),
      messageId: data.messageId,
    };

    // Send notifications to all tokens
    const results = await Promise.allSettled(
      tokens.map(token => 
        sendExpoPushNotification({
          to: token.token,
          title,
          body,
          data: notificationData,
          sound: 'default',
          priority: 'high',
          channelId: 'coach-messages',
        })
      )
    );

    // Process results
    let sentCount = 0;
    const errors: string[] = [];

    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value.success) {
        sentCount++;
      } else {
        const error = result.status === 'rejected' 
          ? result.reason 
          : result.value.error;
        errors.push(`Token ${tokens[index].id}: ${error}`);
        console.error(`[COACH_MESSAGE_NOTIFICATION] Failed to send to token ${tokens[index].id}:`, error);
      }
    });

    console.log(`[COACH_MESSAGE_NOTIFICATION] Sent ${sentCount}/${tokens.length} notifications successfully`);
    
    if (errors.length > 0) {
      console.error(`[COACH_MESSAGE_NOTIFICATION] Errors:`, errors);
    }

    return { 
      success: sentCount > 0 || tokens.length === 0, 
      sentCount, 
      errors 
    };

  } catch (error) {
    console.error("[COACH_MESSAGE_NOTIFICATION] Error sending coach message notification:", error);
    return { 
      success: false, 
      sentCount: 0, 
      errors: [error.message] 
    };
  }
};

/**
 * Send a test push notification
 */
export const sendTestNotification = async (
  channelId: string | number,
  title: string = "Test Notification",
  body: string = "This is a test notification from Awaken"
): Promise<{ success: boolean; sentCount: number; errors: string[] }> => {
  try {
    console.log(`[TEST_NOTIFICATION] Sending test notification to channel ${channelId}`);
    
    const tokens = await getActivePushTokens(channelId);
    
    if (tokens.length === 0) {
      console.log(`[TEST_NOTIFICATION] No active push tokens found for channel ${channelId}`);
      return { success: true, sentCount: 0, errors: [] };
    }

    const results = await Promise.allSettled(
      tokens.map(token => 
        sendExpoPushNotification({
          to: token.token,
          title,
          body,
          data: { type: 'test' },
          sound: 'default',
          priority: 'normal',
        })
      )
    );

    let sentCount = 0;
    const errors: string[] = [];

    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value.success) {
        sentCount++;
      } else {
        const error = result.status === 'rejected' 
          ? result.reason 
          : result.value.error;
        errors.push(`Token ${tokens[index].id}: ${error}`);
      }
    });

    console.log(`[TEST_NOTIFICATION] Sent ${sentCount}/${tokens.length} test notifications successfully`);
    
    return { 
      success: sentCount > 0 || tokens.length === 0, 
      sentCount, 
      errors 
    };

  } catch (error) {
    console.error("[TEST_NOTIFICATION] Error sending test notification:", error);
    return { 
      success: false, 
      sentCount: 0, 
      errors: [error.message] 
    };
  }
};
