"use server";

import Reframe from "@";
import stripe from "../lib/stripe-lib.ts";
import { db } from "../lib/db.ts";
import { sql } from "npm:kysely";
import { PLANS } from "../lib/plans.ts";


// Helper function to get Stripe price ID for plan
function getPriceIdForPlan(planId: string): string {
  const priceMap = {
    'basic_plan_monthly': Reframe.env.STRIPE_BASIC_MONTHLY_PRICE_ID,
    'basic_plan_yearly': Reframe.env.STRIPE_BASIC_YEARLY_PRICE_ID,
    'premium_plan_monthly': Reframe.env.STRIPE_PREMIUM_MONTHLY_PRICE_ID,
    'premium_plan_yearly': Reframe.env.STRIPE_PREMIUM_YEARLY_PRICE_ID
  };
  const priceId = priceMap[planId];
  if (!priceId) {
    throw new Error(`Invalid plan ID: ${planId}`);
  }
  return priceId;
}

function getPlanIdForPriceId(priceId: string): string {
  const priceMap = {
    'basic_plan_monthly': Reframe.env.STRIPE_BASIC_MONTHLY_PRICE_ID,
    'basic_plan_yearly': Reframe.env.STRIPE_BASIC_YEARLY_PRICE_ID,
    'premium_plan_monthly': Reframe.env.STRIPE_PREMIUM_MONTHLY_PRICE_ID,
    'premium_plan_yearly': Reframe.env.STRIPE_PREMIUM_YEARLY_PRICE_ID
  };
  const planId = Object.keys(priceMap).find(key => priceMap[key] === priceId);
  if (!planId) {
    throw new Error(`Invalid price ID: ${priceId}`);
  }
  return planId;
}


export const createTopUpSession = async (
  channelId: string,
  amount: number,
  essenceAmount: number,
  topUpId: string
) => {
  try {
    // Get user data
    const user = await db
      .selectFrom("user")
      .select(["email", "stripeCustomerId"])
      .where("channelId", "=", Number(channelId))
      .executeTakeFirst();

    if (!user) {
      throw new Error("User not found");
    }

    // Create checkout session with price_data
    const session = await stripe.checkout.sessions.create({
      customer: user.stripeCustomerId,
      payment_method_types: ["card"],
      line_items: [{
        price_data: {
          currency: "usd",
          product_data: {
            name: `${essenceAmount} Essence Top-up`,
            description: `Add ${essenceAmount} essence to your Awaken account`
          },
          unit_amount: amount * 100 // amount in cents
        },
        quantity: 1
      }],
      mode: "payment",
      success_url: `${Reframe.env.APP_URL}/chat?top_up_success=true&top_up_id=${topUpId}`,
      cancel_url: `${Reframe.env.APP_URL}/chat`,
      metadata: {
        channelId,
        essenceAmount: essenceAmount.toString(),
        type: "top_up",
        topUpId
      },
      payment_intent_data: {
        description: `Top up: (${essenceAmount} essence added to balance)`
      }
    });

    return session.url;
  } catch (error) {
    console.error("Error creating top-up session:", error);
    throw error;
  }
}; 

export const createCheckoutSession = async (userId: string, planId: string, email: string) => {
  try {
    console.log("[CHECKOUT] Creating session for:", { userId, planId, email });

    // Get or create Stripe customer
    let customer;
    const user = await db
      .selectFrom("user")
      .select(["stripeCustomerId"])
      .where("channelId", "=", Number(userId))
      .executeTakeFirst();

    if (user?.stripeCustomerId) {
      customer = { id: user.stripeCustomerId };
    } else {
      // Create new customer in Stripe
      try {
        customer = await stripe.customers.create(email, { userId });
  
        // Update user with Stripe customer ID
        await db
          .updateTable("user")
          .set({ stripeCustomerId: customer.id })
          .where("channelId", "=", Number(userId))
          .execute();
      } catch (error) {
        console.error("[CHECKOUT] Error creating customer:", error);
        throw error;
      }
    }

    // Get price ID based on plan
    const priceId = getPriceIdForPlan(planId);
    
    // Determine if this is a basic or premium plan
    const isPremiumPlan = planId.startsWith('premium_');
    const isBasicPlan = planId.startsWith('basic_');
    
    // Select the appropriate coupon ID based on plan type
    let couponId = null;
    if (isPremiumPlan) {
      couponId = Reframe.env.STRIPE_PREMIUM_DISCOUNT_COUPON_ID;
    } else if (isBasicPlan) {
      couponId = Reframe.env.STRIPE_BASIC_DISCOUNT_COUPON_ID;
    }

    // Create checkout session options
    const sessionOptions = {
      customer: customer.id,
      payment_method_types: ["card"],
      line_items: [{ price: priceId, quantity: 1 }],
      mode: "subscription",
      success_url: `${Reframe.env.APP_URL}/chat?subscription_success=true&plan=${planId.split('_')[0]}`,
      cancel_url: `${Reframe.env.APP_URL}/chat`,
      metadata: {
        userId,
        planId
      },
    };
    
    // Only add coupon if one is applicable
    if (couponId) {
      sessionOptions.discounts = [
        {
          coupon: couponId
        }
      ];
    }

    // Create checkout session
    const session = await stripe.checkout.sessions.create(sessionOptions);

    console.log("[CHECKOUT] Session created:", session.id);
    return session;

  } catch (error) {
    console.error("[CHECKOUT] Error:", error);
    throw error;
  }
};

export const cancelSubscription = async (subscriptionId: string) => {
  try {
    console.log("[CANCEL] Canceling subscription:", subscriptionId);
    
    // Ensure subscription exists
    const existingSubscription = await db
      .selectFrom("subscription")
      .select(["id", "user_id"])
      .where("id", "=", subscriptionId)
      .executeTakeFirst();
    
    if (!existingSubscription) {
      throw new Error("Subscription not found");
    }
    
    // Update the subscription to cancel at period end
    const updatedSubscription = await stripe.subscriptions.cancel(subscriptionId);
    
    if (!updatedSubscription) {
      throw new Error("Failed to cancel subscription");
    }
    
    // Update the subscription status in the database
    await db
      .updateTable("subscription")
      .set({
        cancelAtPeriodEnd: true
      })
      .where("id", "=", subscriptionId)
      .execute();
    
    console.log("[CANCEL] Successfully canceled subscription for the end of billing period");
    
    return {
      success: true,
      currentPeriodEnd: new Date(updatedSubscription.current_period_end * 1000).toISOString()
    };
  } catch (error) {
    console.error("[CANCEL] Error canceling subscription:", error);
    throw error;
  }
};

export const upgradeSubscription = async (userId: string, newPlanId: string) => {
  try {
    console.log("[UPGRADE] Upgrading subscription for user:", userId, "to plan:", newPlanId);

    // Get user's current subscription
    const user = await db
      .selectFrom("user")
      .select(["stripeSubscriptionId", "currentPlan"])
      .where("channelId", "=", Number(userId))
      .executeTakeFirst();

    if (!user?.stripeSubscriptionId) {
      console.error("[UPGRADE] User does not have an active subscription to upgrade");
      return {
        success: false,
        message: "User does not have an active subscription to upgrade"
      };
    }

    // Get the price ID for the new plan
    const newPriceId = getPriceIdForPlan(newPlanId);
    
    console.log("[UPGRADE] Upgrading subscription:", user.stripeSubscriptionId, "to price:", newPriceId);

    // Upgrade the subscription in Stripe with no proration
    const upgradedSubscription = await stripe.subscriptions.upgrade(
      user.stripeSubscriptionId,
      newPriceId
    );

    if (!upgradedSubscription) {
      console.error("[UPGRADE] Failed to upgrade subscription");
      return {
        success: false,
        message: "Failed to upgrade subscription"
      };
    }

    console.log("[UPGRADE] Successfully upgraded subscription, webhook will handle database updates");

    return {
      success: true,
      subscriptionId: upgradedSubscription.id,
      newPlan: newPlanId
    };

  } catch (error) {
    console.error("[UPGRADE] Error upgrading subscription:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "An unexpected error occurred"
    };
  }
};

const processTopUp = async (channelId: string, essenceAmount: number) => {
  if (!channelId || !essenceAmount) {
    throw new Error("Missing metadata in top-up session");
  }

  try {
    await db
      .updateTable("user")
      .set({
        addedEssenceBalance: sql`added_essence_balance + ${Number(essenceAmount)}`
      })
      .where("channelId", "=", Number(channelId))
      .execute();

    console.log("[WEBHOOK] Top-up processed successfully");
    return new Response("Top-up processed", { status: 200 });
  } catch (error) {
    console.error("[WEBHOOK] Error processing top-up:", error);
    throw error;
  }
}


export const handleWebhookEvent = async (event: any) => {
  console.log("[WEBHOOK] INSIDE THE WEBHOOK FUNCTION");

  // Global deduplication for all Stripe events
  const eventId = event.id;
  const alreadyProcessed = await db
    .selectFrom("stripe_events")
    .select("event_id")
    .where("event_id", "=", eventId)
    .executeTakeFirst();
  if (alreadyProcessed) {
    console.log(`[WEBHOOK] Duplicate event ${eventId} detected, ignoring`);
    return new Response("Duplicate event ignored", { status: 200 });
  }
  await db.insertInto("stripe_events").values({ event_id: eventId }).execute();
  // Handle checkout.session.completed events
  if (event.type === "checkout.session.completed") {
    console.log("[WEBHOOK] Processing checkout session completed");
    const session = event.data.object;
    
    // Handle top-up payments
    if (session.metadata?.type === "top_up") {
      const { channelId, essenceAmount } = session.metadata;
      const response = await processTopUp(channelId, essenceAmount);
      return response;
    }

    return new Response("Webhook handled", { status: 200 });
  } 

  // Handle subscription cancelation events
  else if (event.type === "customer.subscription.deleted" || (event.type === "customer.subscription.updated" && event.data.object.status === "canceled")) {
    console.log("[WEBHOOK] Processing subscription deletion");
    const subscription = event.data.object;
    
    try {
      console.log("[WEBHOOK] Handling subscription deletion:", subscription.id);
      
      // Update the subscription status in the database
      const existingSubscription = await db
        .selectFrom("subscription")
        .select(["id", "user_id"])
        .where("id", "=", subscription.id)
        .executeTakeFirst();
      
      if (existingSubscription) {
        await db
          .updateTable("subscription")
          .set({
            status: "canceled",
            canceledAt: new Date().toISOString()
          })
          .where("id", "=", subscription.id)
          .execute();
        
        console.log("[WEBHOOK] Subscription marked as canceled in database");

        const user = await db
          .selectFrom("user")
          .select(["channelId", "stripeSubscriptionId"])
          .where("stripeSubscriptionId", "=", subscription.id)
          .executeTakeFirst();

        if (!user || user.stripeSubscriptionId !== subscription.id) {
          console.log("[WEBHOOK] Subscription not found for user");
          return;
        }

        await db
          .updateTable("user")
          .set({
            currentPlan: "free_plan",
            planStartDate: null,
            messagesThisPeriod: 0,
            callMinsThisPeriod: 0,
            messageCountToday: 0,
            callMinutesToday: 0,
            callCountToday: 0,
            lastDailyReset: new Date().toISOString(),
            lastMonthlyReset: new Date().toISOString(),
            addedEssenceBalance: sql`added_essence_balance + monthly_essence_balance`,
            monthlyEssenceBalance: PLANS.free.essencePerMonth
          })
          .where("channelId", "=", Number(existingSubscription.userId))
          .execute();
      } else {
        console.log("[WEBHOOK] Subscription not found in database");
      }
    } catch (error) {
      console.error("[WEBHOOK] Failed to process subscription deletion:", error);
      throw error;
    }
  }
  // Handle subscription update events
  else if (event.type === "customer.subscription.updated") {
    console.log("[WEBHOOK] Processing subscription update");
    const subscription = event.data.object;
    const planId = getPlanIdForPriceId(subscription.items.data[0].price.id);
    const customerId = subscription.customer;
    const user = await db
      .selectFrom("user")
      .select(["channelId", "email"])
      .where("stripeCustomerId", "=", customerId)
      .executeTakeFirst();

    try {
      const existingSubscription = await db
        .selectFrom("subscription")
        .select(["id", "user_id", "plan_id"])
        .where("id", "=", subscription.id)
        .executeTakeFirst();

      if (existingSubscription) {
        const planUpgraded = existingSubscription.planId.split("_")[0] === 'basic'
         && planId.split("_")[0] === 'premium';
        console.log("[WEBHOOK] Plan upgraded:", planUpgraded);
        
        await db
          .updateTable("subscription")
          .set({
            status: subscription.status,
            cancelAtPeriodEnd: subscription.cancel_at_period_end,
            planId: planId,
            currentPeriodStart: new Date(subscription.current_period_start * 1000).toISOString(),
            currentPeriodEnd: new Date(subscription.current_period_end * 1000).toISOString(),
          })
          .where("id", "=", subscription.id)
          .execute();

        const user = await db
          .selectFrom("user")
          .select(["channelId", "stripeSubscriptionId"])
          .where("stripeSubscriptionId", "=", subscription.id)
          .executeTakeFirst();

        if (!user || user.stripeSubscriptionId !== subscription.id) {
          console.log("[WEBHOOK] Subscription not found for user");
          return;
        }

        const addedBalanceSql = planUpgraded ? 
            sql`added_essence_balance + monthly_essence_balance` : 
            sql`added_essence_balance`;
        await db
        .updateTable("user")
        .set({
          planStartDate: new Date().toISOString(),
          messagesThisPeriod: 0,
          callMinsThisPeriod: 0,
          messageCountToday: 0,
          callMinutesToday: 0,
          callCountToday: 0,
          lastDailyReset: new Date().toISOString(),
          lastMonthlyReset: new Date().toISOString(),
          addedEssenceBalance: addedBalanceSql,
          monthlyEssenceBalance: PLANS[planId.split("_")[0]].essencePerMonth
        })
        .where("channelId", "=", existingSubscription.userId)
        .execute();
        
        console.log("[WEBHOOK] Subscription updated successfully");
      } else {
        if (subscription.status !== "active") {
          return;
        }
        await db
          .insertInto("subscription")
          .values({
            id: subscription.id,
            userEmail: user.email,
            userId: user.channelId,
            planId: planId,
            cancelAtPeriodEnd: subscription.cancel_at_period_end,
            status: subscription.status,
            createdAt: new Date(subscription.created * 1000).toISOString(),
            currentPeriodStart: new Date(subscription.current_period_start * 1000).toISOString(),
            currentPeriodEnd: new Date(subscription.current_period_end * 1000).toISOString()
          })
          .execute();

        await db
          .updateTable("user")
          .set({
            currentPlan: planId,
            planStartDate: new Date().toISOString(),
            stripeSubscriptionId: subscription.id,
            messagesThisPeriod: 0,
            callMinsThisPeriod: 0,
            messageCountToday: 0,
            callMinutesToday: 0,
            callCountToday: 0,
            lastDailyReset: new Date().toISOString(),
            lastMonthlyReset: new Date().toISOString(),
            addedEssenceBalance: sql`added_essence_balance + monthly_essence_balance`,
            monthlyEssenceBalance: PLANS[planId.split("_")[0]].essencePerMonth
          })
          .where("channelId", "=", Number(user?.channelId))
          .execute();
      }
    } catch (error) {
      console.error("[WEBHOOK] Failed to update subscription:", error);
      throw error;
    }
  }
  else if(event.type === "invoice.payment_failed") {
    // console.log("[WEBHOOK] Processing invoice payment failed");
    // const invoice = event.data.object;
    // const subscriptionId = invoice.subscription;

    // const subscriptionDB = await db
    //   .selectFrom("subscription")
    //   .select(["id", "user_id", "plan_id"])
    //   .where("id", "=", subscriptionId)
    //   .executeTakeFirst();
    
    // if(!subscriptionDB) {
    //   throw new Error("Subscription not found");
    // }

    // const { userId, planId } = subscriptionDB;

    // const subscription = await stripe.subscriptions.retrieve(subscriptionId);

    // if(!subscription) {
    //   throw new Error("Failed to retrieve subscription details");
    // }

    // await db
    //   .updateTable("subscription")
    //   .set({ status: subscription.status })
    //   .where("id", "=", subscriptionId)
    //   .execute();

    // await db
    //   .updateTable("user")
    //   .set({
    //     planStartDate: new Date().toISOString(),
    //     messagesThisPeriod: 0,
    //     callMinsThisPeriod: 0,
    //     messageCountToday: 0,
    //     callMinutesToday: 0,
    //     callCountToday: 0,
    //     lastDailyReset: new Date().toISOString(),
    //     lastMonthlyReset: new Date().toISOString(),
    //     addedEssenceBalance: sql`added_essence_balance + monthly_essence_balance`,
    //     monthlyEssenceBalance: 0
    //   })
    //   .where("channelId", "=", Number(userId))
    //   .execute();
  }

  return new Response("Webhook handled", { status: 200 });
}