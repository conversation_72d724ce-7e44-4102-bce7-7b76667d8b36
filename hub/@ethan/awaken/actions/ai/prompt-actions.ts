"use server";

import Reframe from "@";
import { systemPrompt, 
  soulListenerSystemPrompt, 
  getListenerPayload, 
  getContextPayload, 
  getTrainMessageArray 
} from "../../lib/server-constants.ts";
import { verifyUserPermission } from "../../lib/auth-helper.ts";
import { 
  getMetaFeedback, 
  getProfileText, 
  decryptMessages, 
  decryptProfile, 
  getCoachPrompt,
  getCoachPromptAndTools, 
  generateAudio,
  updateProfileIfNeeded
} from "../../action.ts";
import { TOOL_SCHEMAS } from "../../lib/tool-schemas.ts";
import { geminiRequest, openRouterRequest, claudeRequestRaw } from "../../lib/helper.ts";
import { execTool, ToolUseBlock } from "./tool-executor.ts";
import { PLANS } from "../../lib/plans.ts";
import { getTotalEssenceBalance, getUserData, incrementMessageCount } from "../../lib/db.ts";
import { essenceCost, fetchMultiplier } from "../../lib/essence.ts";
import { PlanType } from "../../lib/plans.ts";
import { saveMessage, processAudioTranscriptionAction, uploadAudio } from "../db/conversation-actions.ts";
import { getActionCost } from "../../lib/user-actions.ts";
import { getCoachAction } from "../../actions/db/coach-actions.ts";
import { transcodeToMp3 } from "../db/conversation-actions.ts";

export const buildStructuredUserPrompt = (
  transcriptOld: string,
  transcriptEnd: string,
  timeInfo: string,
  metaFeedback: string | null = ""
) => {
  /* Builds the text that appears *inside* the Claude user message array.
     Gemini/OpenRouter can use it verbatim. */
  return (
    `<MESSAGE_HISTORY>${transcriptOld}` +
    `${transcriptEnd}` +
    ` ${timeInfo}</MESSAGE_HISTORY>\n` +
    `<FEEDBACK_FROM_META_COACH>${metaFeedback ?? ""}</FEEDBACK_FROM_META_COACH>`
  );
};

export const prepareUserPromptClaudeAction = async (decryptedMessages: any, channelId: string, userMessage: string) => {
    // Order messages by date ascending
    const orderedMessages = decryptedMessages.sort((a, b) => 
      new Date(a.Date).getTime() - new Date(b.Date).getTime()
    );

  
  // SECTION: MAP ITEMS TO STRUCTURE EXPECTED BY ANTHROPIC'S API
  // ----------------------------------------------------------------
    let messages = orderedMessages.map(item => ({
        role: item.Sender,
        content: item.Content
    }));

    // Remove any leading "assistant" messages
    while (messages.length > 0 && messages[0].role === "assistant") {
        messages.shift();
    }

    // Insert a placeholder if two adjacent entries have the same role
    for (let i = 1; i < messages.length; i++) {
        if (messages[i].role === messages[i - 1].role) {
            const alternateRole = messages[i].role === 'user' ? 'assistant' : 'user';
            messages.splice(i, 0, { role: alternateRole, content: "(No message here)" }); // Placeholder text
            i++; // Skip the inserted placeholder to avoid infinite loop
        }
    }

    // Ensure at least one user message is present at the start
    if (messages.length > 0 && messages[0].role !== "user") {
        messages.unshift({ role: "user", content: "(No message here)" });
    }

  // SECTION: DATE & TIME
  // ----------------------------------------------------------------
    // Function to format the date in a more readable form
    function formatTime(date: Date) {
      let hours = date.getHours();
      let minutes = date.getMinutes();
      const ampm = hours >= 12 ? 'PM' : 'AM';
      hours = hours % 12;
      hours = hours ? hours : 12; // the hour '0' should be '12'
      minutes = minutes < 10 ? '0' + minutes : minutes;
      const strTime = hours + ':' + minutes + ' ' + ampm;
      return strTime;
    }

    // Filter records to get those sent by 'user'
    const userRecords = orderedMessages.filter(record => record.Sender === 'user');

    // Initialize a variable to hold the formatted output
    let formattedOutput = "";

    if (userRecords.length >= 2) {
      const lastUserRecordDate = new Date(userRecords[userRecords.length - 1].Date);
      const secondLastUserRecordDate = new Date(userRecords[userRecords.length - 2].Date);

      // Calculate elapsed time in seconds
      const elapsedTimeInSeconds = (lastUserRecordDate.getTime() - secondLastUserRecordDate.getTime()) / 1000;
      const elapsedTimeInMinutes = elapsedTimeInSeconds / 60;
      const elapsedTimeInHours = elapsedTimeInSeconds / 3600;

      // Determine how to display the elapsed time
      let timeDifferenceDisplay;
      if (elapsedTimeInSeconds < 3600) { // Less than 1 hour
          const roundedMinutes = Math.round(elapsedTimeInMinutes);
          timeDifferenceDisplay = `${roundedMinutes} minute${roundedMinutes === 1 ? '' : 's'} after previous message`;
      } else { // 1 hour or more
          const roundedHours = Math.floor(elapsedTimeInHours);
          timeDifferenceDisplay = `${roundedHours} hour${roundedHours === 1 ? '' : 's'} after previous message`;
      }

      // Format the output string
      formattedOutput = `[${lastUserRecordDate.toISOString().substring(0, 10)}, ${formatTime(lastUserRecordDate)}, ${timeDifferenceDisplay}]`;
    }

    const nodeData = messages;

    let transcriptLines: string[] = [];
    let transcriptOldLines: string[] = [];
    let transcriptEndLines: string[] = [];

    // Find indices of last two user messages
    let lastUserIndex = -1;
    let secondLastUserIndex = -1;

    // Search backwards to find last two user messages
    for (let i = nodeData.length - 1; i >= 0; i--) {
      if (nodeData[i].role === 'user') {
        if (lastUserIndex === -1) {
          lastUserIndex = i;
        } else {
          secondLastUserIndex = i;
          break;
        }
      }
    }

    // Iterate over each item in the nodeData
    for (let i = 0; i < nodeData.length; i++) {
      const item = nodeData[i];
      
      // Always add to full transcript
      transcriptLines.push(`${item.role}: ${item.content}`);
      
      // Sort into old vs end based on second last user message
      if (i <= secondLastUserIndex) {
        transcriptOldLines.push(`${item.role}: ${item.content}`);
      } else {
        transcriptEndLines.push(`${item.role}: ${item.content}`);
      }
    }

    const transcript = transcriptLines.join("\n");
    const transcriptOld = transcriptOldLines.join("\n");
    const transcriptEnd = transcriptEndLines.join("\n");

    const fullConvo = transcript;

    // SECTION: EXTRACT RECENT CONVERSATION BETWEEN LAST 2 USER MESSAGES
    // ----------------------------------------------------------------
    // Split the conversation into segments based on "user:" and "assistant:"
    const segments = fullConvo.split(/(user:|assistant:)/).slice(1); // Split and remove the empty segment at the beginning

    // Create pairs of speakers and content
    const pairedSegments: string[] = [];
    for (let i = 0; i < segments.length; i += 2) {
        pairedSegments.push(segments[i] + segments[i + 1]);
    }

    // Find the indices of "user:" segments
    const userIndices = pairedSegments.reduce((indices: number[], segment, index) => {
        if (segment.startsWith("user:")) {
            indices.push(index); // Add index if segment starts with "user:"
        }
        return indices;
    }, []);

    let extractedText = "";    
    // If there are fewer than two "user:" segments, return the full conversation
    
    if (userIndices.length >= 2) {
      // Get the start and end indices for the desired extraction
      const startIndex = userIndices[userIndices.length - 2]; // Second-to-last "user:" index
      const endIndex = userIndices[userIndices.length - 1];   // Last "user:" index

      // Extract the segments from the second-to-last "user:" to the last "user:"
      extractedText = pairedSegments.slice(startIndex, endIndex + 1).join("")
    }
    else {
      extractedText = fullConvo;
    }
    // ----------------------------
    
    const messageArray = [
      {
        "role": "user",
        "content": extractedText.trim()
      }
    ];

    const config = {
      model: "claude-3.5-sonnet-20241022",
      system: systemPrompt,
      messages: messageArray,
      temperature: 0.15,
      max_tokens: 2200
    }

  // Construct the array for Sonnet call - just user prompt, system is sent separately
  const trainMessageArray = getTrainMessageArray(extractedText);

  const soulConfig = {
    model: "claude-3.5-sonnet-20241022",
    system: soulListenerSystemPrompt,
    messages: trainMessageArray,
    temperature: 0.15,
    max_tokens: 2200
  }

  const listenSystemPrompt = soulListenerSystemPrompt;

  // Adapt the message array for Gemini:
  // Each message now has "parts" instead of "content".
  // Also, note that the role values remain "user" and "assistant" (these are acceptable for Gemini).
  const listenerPayload = getListenerPayload(extractedText, listenSystemPrompt);

  // 2nd request = CONTEXT CHECKER
  const contextSystemPrompt = systemPrompt;
  const contextPayload = getContextPayload(extractedText, contextSystemPrompt);

  return {
    transcript,
    transcriptOld,
    transcriptEnd,
    timeInfo: formattedOutput,
    config,
    soulConfig,
    contextPayload,
    listenerPayload,
  }
} 

export const prepareClaudePayloadAction = async (
  channelId,
  userMessage,
  metaFeedback,
  toneNotes,
  selectedCoach,
  modelName: string,
  isVoiceNote: boolean = false,
  useThinking: boolean = false
) => {
  console.time('magic-route-total');
  console.log('[CLAUDE_PAYLOAD] Starting with selectedCoach:', selectedCoach);
  console.log('[CLAUDE_PAYLOAD] isVoiceNote:', isVoiceNote);

  console.time('magic-initial-data-fetch');
  const [profileData, messagesData] = await Promise.all([
    getProfileText(channelId),
    decryptMessages(channelId, true, selectedCoach, 25, ["message", "proactive_message"])
  ]);
  console.timeEnd('magic-initial-data-fetch');
  console.log('[CLAUDE_PAYLOAD] Fetched messages for coach:', selectedCoach);

  const { profileText } = profileData;
  const decryptedMessages = messagesData;

  // Check if messages are coming from the correct coach
  if (decryptedMessages && decryptedMessages.length > 0) {
    const foundCoaches = [...new Set(decryptedMessages.map(msg => msg.CoachName || 'unknown'))];
    console.log('[CLAUDE_PAYLOAD] Messages found from coaches:', foundCoaches);
    console.log('[CLAUDE_PAYLOAD] Expected coach:', selectedCoach);
    console.log('[CLAUDE_PAYLOAD] First message sample:', decryptedMessages[0]);
  } else {
    console.log('[CLAUDE_PAYLOAD] No messages found for coach:', selectedCoach);
  }

  console.time('magic-do-magic');
  // If doMagic primarily helps you parse context from messages, 
  // you can still call it to keep consistency, or skip it entirely 
  // if its only purpose was to assist Gemini.
  const { 
    // we can still grab these, but they won't be used for Gemini
    contextPayload, 
    listenerPayload, 
    timeInfo, 
    transcript, 
    transcriptOld, 
    transcriptEnd 
  } = await prepareUserPromptClaudeAction(decryptedMessages, channelId, userMessage);
  console.timeEnd('magic-do-magic');

  // --- Removed Gemini Request Block ---
  // We skip calling geminiRequest(...) and parsing text1 / text2
  // so no context is extracted from Gemini, no <CONTEXT>, no <I_SENSE_IN_THEIR_BEING>

  console.time('magic-decrypt-profile');
  console.timeEnd('magic-decrypt-profile');

  console.time('magic-prepare-response');

  // Instead of using Gemini results, we skip them or set them empty:
  let extractedBeing = "";   // was formerly derived from <final> block
  let contextText = "";      // was formerly derived from <context> block

  // The transcript we keep as is, but no <CONTEXT> or <I_SENSE_IN_THEIR_BEING>
  let oldTranscript = `<MESSAGE_HISTORY>${transcriptOld}`;
  let timeContextFeedback = ` ${timeInfo}</MESSAGE_HISTORY>\n<FEEDBACK_FROM_META_COACH>${metaFeedback}</FEEDBACK_FROM_META_COACH>\n`;

  // Get the coach's system prompt instead of using getSystemPrompt1/2
  console.time('magic-get-coach-prompt');
  console.log('[CLAUDE_PAYLOAD] Getting coach prompt for:', selectedCoach);
  
  const promptKey = isVoiceNote ? "reflectionPrompt" : "messagePrompt";
  const { promptData, toolsConfig } = await getCoachPromptAndTools(selectedCoach, promptKey);
  const { SystemPrompt } = promptData || { SystemPrompt: "" };
  
  console.log('[CLAUDE_PAYLOAD] Retrieved coach prompt, length:', SystemPrompt?.length || 0);
  console.log('[CLAUDE_PAYLOAD] Tools config:', toolsConfig);
  console.timeEnd('magic-get-coach-prompt');
  
  // Replace {{CLIENT_PROFILE}} placeholder in system instruction
  let systemInstruction = SystemPrompt || "You are a helpful coach."; // Fallback system prompt
  systemInstruction = systemInstruction.replace('{{CLIENT_PROFILE}}', profileText || '');

  // Omit <I_SENSE_IN_THEIR_BEING> from the user content
  // (previously: let timeContextFeedbackBeing = 
  //    `${timeContextFeedback}<I_SENSE_IN_THEIR_BEING>\n${extractedBeing}\n</I_SENSE_IN_THEIR_BEING>\n`
  // )
  let timeContextFeedbackBeing = `${timeContextFeedback}`; 

  // console.log("oldTranscript", oldTranscript);
  // console.log("transcriptEnd", transcriptEnd);
  // console.log("timeContextFeedbackBeing", timeContextFeedbackBeing);
  
  const systemArray = [
    {
      "type": "text",
      "text": systemInstruction,
      "cache_control": { "type": "ephemeral" }
    }
  ];

  const messageArray = [
    {
      "role": "user",
      "content": [
        {
          "type": "text",
          "text": oldTranscript,
          "cache_control": { "type": "ephemeral" }
        },
        {
          "type": "text",
          "text": transcriptEnd,
          "cache_control": { "type": "ephemeral" }
        },
        {
          "type": "text",
          "text": timeContextFeedbackBeing
        }
      ]
    }
  ];
  console.timeEnd('magic-prepare-response');

  console.timeEnd('magic-route-total');

  const thinking = {
    "type": "enabled",
    "budget_tokens": 1400
  };

  const baseMaxTokens = 700; // Or whatever base value you prefer

  const outputPayload: any = {
    model: modelName,
    system: systemArray,
    messages: messageArray,
    temperature: 1,
    max_tokens: baseMaxTokens // Start with base value
  };

  if ((isVoiceNote || useThinking) && !toolsConfig?.length) {
    outputPayload.thinking = thinking;
    outputPayload.max_tokens += thinking.budget_tokens; // Add thinking budget
    outputPayload.model = modelName;
  }

  // Attach Anthropic tools if configured
  if (toolsConfig?.length) {
    outputPayload.tools = toolsConfig
      .map(t => TOOL_SCHEMAS.anthropic[t.name])
      .filter(Boolean);
  }

  return {
    output: outputPayload,
    toolsConfig  // Pass toolsConfig for tool execution context
  };
}; 

export const prepareGeminiPayloadAction = async (channelId: string, userMessage: string, selectedCoach: string | undefined, isVoiceNote: boolean = false) => {
  console.time('gemini-route-total');
  console.log('[GEMINI_PAYLOAD] Starting with selectedCoach:', selectedCoach);
  console.log('[GEMINI_PAYLOAD] isVoiceNote:', isVoiceNote);

  console.time('gemini-initial-data-fetch');
  const [systemPromptData, profileData, messagesData] = await Promise.all([
    getCoachPrompt(selectedCoach || "Kokoro", isVoiceNote ? "reflectionPrompt" : "messagePrompt"),
    getProfileText(channelId),
    decryptMessages(channelId, true, selectedCoach, 25, ["message", "proactive_message"])
  ]);
  console.log("gemini messagesData", messagesData);
  console.timeEnd('gemini-initial-data-fetch');
  console.log('[GEMINI_PAYLOAD] Fetched messages for coach:', selectedCoach);

  const { SystemPrompt } = systemPromptData;
  const { profileText } = profileData;
  const decryptedMessages = messagesData;
  
  // Check if messages are coming from the correct coach
  if (decryptedMessages && decryptedMessages.length > 0) {
    const foundCoaches = [...new Set(decryptedMessages.map(msg => msg.CoachName || 'unknown'))];
    console.log('[GEMINI_PAYLOAD] Messages found from coaches:', foundCoaches);
    console.log('[GEMINI_PAYLOAD] Expected coach:', selectedCoach);
    console.log('[GEMINI_PAYLOAD] First message sample:', decryptedMessages[0]);
  } else {
    console.log('[GEMINI_PAYLOAD] No messages found for coach:', selectedCoach);
  }

  // --- 2025-05-09 • Uniform prompt structure shared with Claude (see buildStructuredUserPrompt) ---
  // add – we already have decryptedMessages, etc.
  const {
    transcriptOld,
    transcriptEnd,
    timeInfo, // <- ← from prepareUserPromptClaudeAction
  } = await prepareUserPromptClaudeAction(
    decryptedMessages,
    channelId,
    userMessage
  );

  // build metaFeedback exactly like Claude does
  const { metaFeedback } = await getMetaFeedback(channelId, selectedCoach || "Kokoro");

  // Format the conversation history
  const formattedMessages = decryptedMessages
    .sort((a, b) => new Date(a.Date).getTime() - new Date(b.Date).getTime())
    .map(msg => `${msg.Sender}: ${msg.Content}`)
    .join("\n");

  // Build the Gemini payload
  const systemInstructionText = (SystemPrompt || "You are a helpful coach using Gemini.").replace('{{CLIENT_PROFILE}}', profileText || '');

  const payload = {
    "system_instruction": {
      "parts": {
        "text": systemInstructionText
      }
    },
    "contents": [
      {
        "role": "user",
        "parts": [
          {
            "text": `${buildStructuredUserPrompt(transcriptOld, transcriptEnd, timeInfo, metaFeedback)}`
          }
        ]
      }
    ],
    "generationConfig": {
      "temperature": 1.0
    },
    "safetySettings": [
      {
        "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
        "threshold": "BLOCK_NONE"
      },
      {
        "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
        "threshold": "BLOCK_NONE"
      },
      {
        "category": "HARM_CATEGORY_HATE_SPEECH",
        "threshold": "BLOCK_NONE"
      },
      {
        "category": "HARM_CATEGORY_HARASSMENT",
        "threshold": "BLOCK_NONE"
      }
    ]
  };

  console.timeEnd('gemini-route-total');
  return payload;
}

export const createMessageResponse = async (
  channelId: string, 
  message: string | ArrayBuffer, 
  coachName: string,
  isVoiceNote: boolean = false,
  useAudio: boolean = false,
  model: "claude" | "gemini" | "openrouter" = "claude"
) => {
  if(!(await verifyUserPermission(channelId))) {
    return null;
  }
  const userData = await getUserData(channelId);
  console.log('userData', userData);
  const userPlanType = (userData.subscription?.planId.split('_')[0] ?? 'free') as PlanType;
  const selectedCoach = userData.selectedCoach || "Kokoro";
  
  console.log('[MESSAGE] Processing message for user:', channelId);
  console.log('[MESSAGE] Selected coach from subscription:', selectedCoach);
  
  // console.log(userPlanType, PLANS);
  // Temporarily disabled daily message limit check
  // if(PLANS[userPlanType].limits.messagesPerDay <= userData.messageCountToday) {
  //   return {
  //     blocked: "daily_msg_limit_reached" 
  //   }
  // }
  // Check monthly message limit
  if(getActionCost("message", 1) > await getTotalEssenceBalance(channelId)) {
    return {
      blocked: "monthly_msg_limit_reached",
      action: userPlanType === "free" ? "checkout" : "top_up_xyz"
    }
  }

  const coach = await getCoachAction(coachName, channelId);

  // Pick the same voice-selection logic we use in buildCallAssistantConfig
  const voiceForTTS = coach?.selectedVoice?.voice_id
    ?? (typeof coach?.metadata === "string"
          ? JSON.parse(coach.metadata)?.voices?.[0]?.voice_id
          : coach?.metadata?.voices?.[0]?.voice_id)
    ?? "OPp59xqerF9pc477FJM8";

  let messageText: string;
  if(message instanceof ArrayBuffer) {
    messageText = await processAudioTranscriptionAction(message, channelId);
  } else {
    messageText = message;
  }

  console.log("Channel ID: ", channelId);
  console.log("Coach Name: ", coachName);
  console.log("Is Voice Note: ", isVoiceNote);
  console.log("Use Audio: ", useAudio);

  
  
  const encoder = new TextEncoder();
  const stream = new ReadableStream({
    async start(controller) {
      if(message instanceof ArrayBuffer) {
        // send the transcript first
        console.log("SEND THE TRANSCRIPT FIRST")
        controller.enqueue(encoder.encode(JSON.stringify({ transcript: messageText })));
      }

          // Set default model details
      let modelProvider: "anthropic" | "google" | "openrouter" | string = "anthropic"; // Default provider
      let modelName = "claude-3-5-sonnet-20241022"; // Default model name
      let determinedModelType: "claude" | "gemini" | "openrouter" = "claude"; // Default model type for createMessageResponse
      let useThinking = false;              // NEW ── chat needs Claude-thinking

      if (coach?.metadata) {
        const metadata = typeof coach.metadata === 'string' 
          ? JSON.parse(coach.metadata) 
          : coach.metadata;
        
        /* ── A. legacy call-model ────────────────────────────────────────── */
        if (metadata.model && Array.isArray(metadata.model) && metadata.model.length > 0) {
          const modelConfig = metadata.model[0];
          modelProvider = modelConfig.provider?.toLowerCase() || modelProvider; // Ensure lowercase comparison
          modelName = modelConfig.model || modelName;
          // Determine the model type for createMessageResponse based on provider
          if (modelProvider === "google") {
            determinedModelType = "gemini";
          } else if (modelProvider === "openrouter") {
            determinedModelType = "openrouter";
          } else { // Default to claude for anthropic or unknown providers
            determinedModelType = "claude"; 
          }
        }

        /* ── B. chat-specific override (messages) ────────────────────────── */
        if (!isVoiceNote && metadata.messageModel?.model) {
          const chatCfg       = metadata.messageModel;

          modelProvider       = (chatCfg.provider || "anthropic").toLowerCase();
          modelName           = chatCfg.model;

          // choose the right branch
          if (modelProvider === "google") {
            determinedModelType = "gemini";
          } else if (modelProvider === "openrouter") {
            determinedModelType = "openrouter";
          } else {                          // default: Anthropic
            determinedModelType = "claude";
            if (chatCfg.thinking) {         // thinking only exists on Anthropic
              useThinking = true;
            }
          }
        }

        /* ── C. voice note override ────────────────────────── */
        if (isVoiceNote && metadata.voiceNoteModel?.model) {
          const voiceNoteCfg = metadata.voiceNoteModel;
          
          modelProvider = (voiceNoteCfg.provider || "anthropic").toLowerCase();
          modelName = voiceNoteCfg.model;
          
          // choose the right branch
          if (modelProvider === "google") {
            determinedModelType = "gemini";
          } else if (modelProvider === "openrouter") {
            determinedModelType = "openrouter";
          } else {
            determinedModelType = "claude";
            if (voiceNoteCfg.thinking) {
              useThinking = true;
            }
          }
        }
      } else {
          console.log("[SendMessage] No coach metadata found, using defaults.");
      }

      
      console.log('[MESSAGE] Using model:', model);
      if (determinedModelType === "gemini") {
        console.log('[MESSAGE] Using Gemini model:', modelName);
      } else if (determinedModelType === "openrouter") {
        console.log('[MESSAGE] Using OpenRouter model:', modelName);
      } else {
        console.log('[MESSAGE] Using Claude model:', modelName,
                  useThinking ? '(thinking enabled)' : '');
      }

      console.log(channelId, messageText);
      console.timeEnd('createMessageResponse-parse-request');

      console.time('createMessageResponse-fetch-metadata');

      // console.log('metadataResponse', metadataResponse);
      const { metaFeedback } = await getMetaFeedback(channelId, selectedCoach);
      console.timeEnd('createMessageResponse-fetch-metadata');
      console.log("Creating new date in app.tsx", new Date().toISOString());

      // // NEW - Encrypt user message and add to database
      try {
        // Use the shared function with parameters in the correct order
        const encryptUserData = await saveMessage(
          channelId,
          "user",
          messageText,
          new Date().toISOString(),
          "Default", // status (default "Default")
          "message",
          selectedCoach
        );
        console.log("Encrypted User Data:", encryptUserData);
        const userMessageId = encryptUserData?.id;

        if(message instanceof ArrayBuffer) {
          const blob = new Blob([await transcodeToMp3(message)], { type: "audio/mp3" });
          await uploadAudio(blob, channelId, userMessageId);
        }

        let aiResponse;

        if (determinedModelType === "gemini") {
          console.time('createMessageResponse-gemini-request');
          
          // Prepare payload for Gemini
          const geminiPayload = await prepareGeminiPayloadAction(channelId, message, selectedCoach, isVoiceNote);
          
          // Call Gemini with the specified model name
          const geminiResponse = await geminiRequest([geminiPayload], modelName);
          
          // Parse response
          const data = await geminiResponse.json();
          // console.log("DATA FROM GEMINI", data);
          aiResponse = data.text1;
          
          console.timeEnd('createMessageResponse-gemini-request');
        } else if (determinedModelType === "openrouter") {
          console.time('createMessageResponse-openrouter-request');

          // Fetch necessary data (similar to Gemini)

          const coachPrompt = await getCoachPrompt(selectedCoach || "Kokoro", isVoiceNote ? "reflectionPrompt" : "messagePrompt");
          const [systemPromptData, profileData, messagesData] = await Promise.all([
            coachPrompt,
            getProfileText(channelId),
            decryptMessages(channelId, true, selectedCoach, 25, ["message", "proactive_message"])
          ]);
          
          const { SystemPrompt } = systemPromptData;
          const { profileText } = profileData;
          const decryptedMessages = messagesData;

          // Format the conversation history
          const formattedMessages = decryptedMessages
            .sort((a, b) => new Date(a.Date).getTime() - new Date(b.Date).getTime())
            .map(msg => `${msg.Sender}: ${msg.Content}`)
            .join("\n");

          // Prepare system instruction and user prompt
          let systemInstruction = SystemPrompt || "You are a helpful coach."; // Fallback system prompt
          // Replace placeholder in system instruction
          systemInstruction = systemInstruction.replace('{{CLIENT_PROFILE}}', profileText || '');

          // --- 2025-05-09 • Uniform prompt structure shared with Claude (see buildStructuredUserPrompt) ---
          const {
              transcriptOld,
              transcriptEnd,
              timeInfo
          } = await prepareUserPromptClaudeAction(decryptedMessages, channelId, messageText);

          const userPrompt =
            `${buildStructuredUserPrompt(transcriptOld, transcriptEnd, timeInfo, "")}`;

          // Call OpenRouter API
          aiResponse = await openRouterRequest(modelName, systemInstruction, userPrompt);

          // console.log("DATA FROM OPENROUTER", aiResponse);
          console.timeEnd('createMessageResponse-openrouter-request');
        } else {
          console.time('createMessageResponse-handle-magic');
          let msg = messageText.replace(/\n/g, "").replace(/'/g, "");

          // NEW LOG
          console.log("calling prepareClaudePayloadAction:", msg);
          const response = await prepareClaudePayloadAction(channelId, msg, metaFeedback, null, selectedCoach, modelName, isVoiceNote, useThinking);
          console.timeEnd('createMessageResponse-handle-magic');

          console.time('createMessageResponse-claude-request');
          // console.log("[CLAUDE] Payload:", JSON.stringify(response.output, null, 2));
          
          // First Claude request using the new helper
          let assistantTurn = await claudeRequestRaw(response.output);
          
          // Tool-use loop
          while (
            Reframe.env.CLAUDE_TOOLS_ENABLED === "1" &&
            assistantTurn.stop_reason === "tool_use"
          ) {
            const toolBlocks: ToolUseBlock[] = assistantTurn.content.filter(
              (c: any) => c.type === "tool_use"
            );

            console.log("[TOOL_USE]", selectedCoach, toolBlocks.map(b => b.name));

            // Merge coach's tool config with Claude's input
            const results = await Promise.all(
              toolBlocks.map((blk) => {
                // Find the coach's config for this tool
                const coachToolConfig = response.toolsConfig?.find(
                  (t) => t.name === blk.name
                );
                
                // Merge coach parameters with Claude's input
                const mergedInput = {
                  ...blk.input,
                  ...(coachToolConfig || {}), // Includes kb_identifier
                };
                
                return execTool({ ...blk, input: mergedInput });
              })
            );

            const toolMsgs = toolBlocks.map((blk, idx) => ({
              role: "user",
              content: [
                {
                  type: "tool_result",
                  tool_use_id: blk.id,
                  content: JSON.stringify(results[idx]),
                },
              ],
            }));

            assistantTurn = await claudeRequestRaw({
              system: response.output.system,
              messages: [
                ...response.output.messages,
                { role: "assistant", content: assistantTurn.content },
                ...toolMsgs,
              ],
              tools: response.output.tools,
              temperature: response.output.temperature,
              max_tokens: response.output.max_tokens,
              model: response.output.model,
              ...(response.output.thinking && { thinking: response.output.thinking }),
            });
          }

          console.timeEnd('createMessageResponse-claude-request');
          
          // Extract final text robustly (Step 6)
          aiResponse = assistantTurn.content
            .filter((c: any) => c.type === "text")
            .map((c: any) => c.text)
            .join("\n")
            .replace(/<thinking>[\s\S]*?<\/thinking>/g, "");
        }

        // Determine Essence cost with coach multiplier, then increment message count
        try {
          const mult = await fetchMultiplier(selectedCoach);
          const baseCost = getActionCost("message", 1);
          const messageCost = essenceCost(baseCost, mult);
          if(!Number.isFinite(messageCost)) {
            console.error("[Essence] Invalid message cost", { baseCost, mult, messageCost });
            throw new Error("Invalid message cost");
          }
          await incrementMessageCount(channelId, messageCost);
        } catch (err) {
          console.error("[Essence] Error applying cost multiplier:", err);
          await incrementMessageCount(channelId); // Fallback to base cost
        }

        console.timeEnd('createMessageResponse-route-total');

        const encryptAssistantData = await saveMessage(
          channelId,
          "assistant",
          aiResponse,
          new Date().toISOString(),
          "Default", // status (default "Default")
          "message",
          selectedCoach
        );
        console.log("Encrypted Assistant Data:", encryptAssistantData);
        const assistantMessageId = encryptAssistantData.id;

        // console.log("RETURNING DATA FROM ACTION", aiResponse);
        let audioData = null;
        let uploadedAudio = null;
        if(useAudio) {
          audioData = await generateAudio(aiResponse, { id: voiceForTTS });
          const audioBlob = new Blob([audioData], { type: "audio/webm;codecs=opus" });
          uploadedAudio = await uploadAudio(audioBlob, channelId, assistantMessageId);
        }
        // return { 
        //   output: aiResponse,
        //   userMessageId,
        //   assistantMessageId,
        //   uploadedAudio,
        //   messageText
        // };
        controller.enqueue(encoder.encode(JSON.stringify({
          output: aiResponse,
          userMessageId,
          assistantMessageId,
          uploadedAudio,
          messageText
        })));

        controller.close();

        console.log("[METAFEEDBACK_DEBUG] Calling updateProfileIfNeeded from createMessageResponse with:", { channelId, selectedCoach });
        updateProfileIfNeeded(channelId, selectedCoach);
      } catch (error) {
        console.error("Error encrypting or uploading audio:", error);
        throw error;
      }
    },
  });

  return stream;
}