"use server";

import Reframe from "@";
import { 
  getProfileText, 
  decryptProfile, 
  decryptMessages,
  getCoachPromptAndTools,
  processTranscriptAction,
  initiateProfile,
  updateProfileIfNeeded,
  updateProfileText
} from "../../action.ts";
import { geminiRequest } from "../../lib/helper.ts";
import { verifyUserPermission } from "../../lib/auth-helper.ts";
import { db, getTotalEssenceBalance, getUserData, incrementCallMinutes } from "../../lib/db.ts";
import { PLANS } from "../../lib/plans.ts";
import { vapiGoalCallEndUrl, n8nFlowUrl } from "../../lib/server-constants.ts";
import { PlanType } from "../../lib/plans.ts";
import { saveMessage, prepMessageHistoryAction } from "../db/conversation-actions.ts";
import { getCoachAction } from "../db/coach-actions.ts";
import { getActionCost } from "../../lib/user-actions.ts";
import { generateGuidedSessionPlanAction } from "./session-plan-actions.ts";
import { SUPERUSER_EMAILS, summarizerPrompt } from "../../lib/server-constants.ts";
import { TOOL_VAPI_CONFIGS } from "../../lib/tool-schemas.ts";
import { getUserInfo } from "../../lib/get-user-info.ts";
import { v4 as uuid } from "npm:uuid";
import { essenceCost, fetchMultiplier } from "../../lib/essence.ts";

interface GeminiResponse {
  success: string;
  text1?: string;
  text2?: string;
  [key: string]: any;
}

// Define the CardContext type based on the plan
interface CardContext {
  userCardId?: string | null; // Make userCardId optional and nullable
  coachOfferingId: string; // Add offering ID
  type: string; // e.g., 'EXPLORE', 'GOAL', 'GUIDED_SESSION'
  data?: {
    sessionPlan?: string; // Example data field for guided sessions
    goal?: string; // Optional goal stored in data
    [key: string]: any; // Allow other data fields
  };
  uiConfig?: any;
  goalInput?: string; // Input specifically for goal-type cards from the UI
}

/**
 * Builds a configuration for a coaching call with Claude LLM
 *
 * @param channelId string - The user's channel ID
 * @param type string - The type of call: 'goal', 'normal', or 'firstCall'
 * @param goalInput string | undefined - The user's stated goal for the call (when type='goal')
 * @param maxCallDuration number - Maximum call duration in seconds (default: 1500)
 * @param coachName string - The name of the coach (default: "Kokoro")
 * @returns {Promise<any>} The JSON config that the route can directly return to the client
 */
export async function buildCallAssistantConfig(
  channelId: string,
  type: 'goal' | 'normal' | 'firstCall',
  goalInput?: string,
  maxCallDuration: number = 1500,
  coachName: string = "Kokoro"
): Promise<any> {
  console.log(`[${type.toUpperCase()} CALL] Starting to build ${type} call assistant config`);
  console.log(`[${type.toUpperCase()} CALL] Params:`, { channelId, type, goalInput, maxCallDuration, coachName });

  try {
    // Fetch user email to check for superuser status
    console.log(`[${type.toUpperCase()} CALL] Fetching user email for superuser check`);
    const user = await db
      .selectFrom("user")
      .select("email")
      .where("channelId", "=", Number(channelId)) // Ensure channelId is a number if needed by DB schema
      .executeTakeFirst();
    const userEmail = user?.email;
    console.log(`[${type.toUpperCase()} CALL] SUPERUSER_EMAILS:`, SUPERUSER_EMAILS);
    console.log(`[${type.toUpperCase()} CALL] SUPERUSER_EMAILS type:`, typeof SUPERUSER_EMAILS);
    console.log(`[${type.toUpperCase()} CALL] SUPERUSER_EMAILS is array:`, Array.isArray(SUPERUSER_EMAILS));
    const isSuperuser = userEmail && Array.isArray(SUPERUSER_EMAILS) ? SUPERUSER_EMAILS.includes(userEmail) : false;
    const enableRecording = isSuperuser; // Set recording based on superuser status
    console.log(`[${type.toUpperCase()} CALL] User email: ${userEmail}, Is Superuser: ${isSuperuser}, Recording Enabled: ${enableRecording}`);

    // Get coach data including user's selected voice preference
    console.log(`[${type.toUpperCase()} CALL] Fetching coach data with user voice preference`);
    const coach = await getCoachAction(coachName, channelId);
    
    if (!coach) {
      console.error(`[${type.toUpperCase()} CALL] Coach not found: ${coachName}`);
      throw new Error(`Coach not found: ${coachName}`);
    }
    
    // Set up voice config using the selected voice or default
    let voiceId = "OPp59xqerF9pc477FJM8"; // Default fallback
    let stability = 0.35;
    let similarityBoost = 0.75;
    let selectedVoiceName = "KokoroMale"; // Default voice name
    let model = "eleven_turbo_v2"; // Default model
    let speed = 1.0; // Default speed
    let style = 0.0; // Default style
    let useSpeakerBoost = false; // Default speaker boost
    let optimizeStreamingLatency = 3; // Default streaming latency
    
    if (coach.selectedVoice) {
      // Using the user's selected voice
      voiceId = coach.selectedVoice.voice_id;
      stability = coach.selectedVoice.voice_settings?.stability || stability;
      similarityBoost = coach.selectedVoice.voice_settings?.similarity_boost || similarityBoost;
      selectedVoiceName = coach.selectedVoice.voice_name;
      model = coach.selectedVoice.model || model;
      speed = coach.selectedVoice.speed || speed;
      style = coach.selectedVoice.voice_settings?.style || style;
      useSpeakerBoost = coach.selectedVoice.voice_settings?.useSpeakerBoost ?? useSpeakerBoost; // Use nullish coalescing for boolean
      optimizeStreamingLatency = coach.selectedVoice.voice_settings?.optimizeStreamingLatency || optimizeStreamingLatency;
    } else if (coach.metadata) {
      // No user selection, use first voice in metadata if available
      const metadata = typeof coach.metadata === 'string' 
        ? JSON.parse(coach.metadata) 
        : coach.metadata;
      
      if (metadata.voices && metadata.voices.length > 0) {
        const defaultVoice = metadata.voices[0];
        voiceId = defaultVoice.voice_id;
        stability = defaultVoice.voice_settings?.stability || stability;
        similarityBoost = defaultVoice.voice_settings?.similarity_boost || similarityBoost;
        selectedVoiceName = defaultVoice.voice_name;
        model = defaultVoice.model || model;
        speed = defaultVoice.speed || speed;
        style = defaultVoice.voice_settings?.style || style;
        useSpeakerBoost = defaultVoice.voice_settings?.useSpeakerBoost ?? useSpeakerBoost; // Use nullish coalescing for boolean
        optimizeStreamingLatency = defaultVoice.voice_settings?.optimizeStreamingLatency || optimizeStreamingLatency;
      }
    }
    
    console.log(`[${type.toUpperCase()} CALL] Using voice: ${selectedVoiceName} (${voiceId}), model: ${model}, speed: ${speed}, style: ${style}, speakerBoost: ${useSpeakerBoost}, streamingLatency: ${optimizeStreamingLatency}`);

    // Set default model details
    let modelProvider = "anthropic";
    let modelName = "claude-3-5-sonnet-20241022";

    // Check metadata for model override
    if (coach.metadata) {
      const metadata = typeof coach.metadata === 'string' 
        ? JSON.parse(coach.metadata) 
        : coach.metadata;
      
      if (metadata.model && Array.isArray(metadata.model) && metadata.model.length > 0) {
        const modelConfig = metadata.model[0];
        modelProvider = modelConfig.provider || modelProvider;
        modelName = modelConfig.model || modelName;
        console.log(`[${type.toUpperCase()} CALL] Overriding model with metadata: Provider=${modelProvider}, Model=${modelName}`);
      }
    }

    // Set default background sound and check metadata for override
    let backgroundSound = "off";
    if (coach.metadata) {
      const metadata = typeof coach.metadata === 'string' 
        ? JSON.parse(coach.metadata) 
        : coach.metadata;
      
      if (metadata.backgroundSound) {
        backgroundSound = metadata.backgroundSound;
        console.log(`[${type.toUpperCase()} CALL] Using custom background sound: ${backgroundSound}`);
      }
    }

    // Set default transcriber config and check metadata for override
    let transcriberConfig: any = {
      model: "nova-3",
      language: "en",
      provider: "deepgram",
      endpointing: null
    };
    
    if (coach.metadata) {
      const metadata = typeof coach.metadata === 'string' 
        ? JSON.parse(coach.metadata) 
        : coach.metadata;
      
      if (metadata.transcriber) {
        // Override with metadata transcriber config
        transcriberConfig = {
          model: metadata.transcriber.model || transcriberConfig.model,
          language: metadata.transcriber.language || transcriberConfig.language,
          provider: metadata.transcriber.provider || transcriberConfig.provider,
          endpointing: metadata.transcriber.endpointing !== undefined ? metadata.transcriber.endpointing : transcriberConfig.endpointing,
          // Include keywords if present in metadata
          ...(metadata.transcriber.keywords ? { keywords: metadata.transcriber.keywords } : {})
        };
        console.log(`[${type.toUpperCase()} CALL] Using custom transcriber config:`, transcriberConfig);
      }
    }

    // 1) Fetch user profile text from Airtable
    console.log(`[${type.toUpperCase()} CALL] Fetching user profile`);
    const { profileText } = await getProfileText(channelId);
    console.log(`[${type.toUpperCase()} CALL] Profile fetched successfully`);

    // 3) Fetch user's conversation & decrypt
    console.log(`[${type.toUpperCase()} CALL] Fetching and decrypting messages`);
    const messages = await decryptMessages(channelId, true, coachName, 25, ["message", "proactive_message"]);
    console.log(`[${type.toUpperCase()} CALL] Messages fetched and decrypted successfully`);

    // 4 & 5) Use the new prepMessageHistoryAction to prepare conversation text and time elapsed note
    console.log(`[${type.toUpperCase()} CALL] Preparing message history`);
    
    // Log message details for testing
    console.log(`[${type.toUpperCase()} CALL] Retrieved ${messages.length} messages for context`);
    if (messages.length > 0) {
      const messageTypeCounts = messages.reduce((acc: any, msg: any) => {
        acc[msg.Type] = (acc[msg.Type] || 0) + 1;
        return acc;
      }, {});
      console.log(`[${type.toUpperCase()} CALL] Message type breakdown:`, messageTypeCounts);
      console.log(`[${type.toUpperCase()} CALL] Sample messages:`, messages.slice(0, 3).map((m: any) => ({ Type: m.Type, Sender: m.Sender, Content: m.Content.substring(0, 50) + '...' })));
    }
    
    const { conversationText, timeElapsedNote } = prepMessageHistoryAction(messages);
    console.log(`[${type.toUpperCase()} CALL] Message history prepared`);
    console.log(`[${type.toUpperCase()} CALL] Conversation text length: ${conversationText.length} characters`);
    console.log(`[${type.toUpperCase()} CALL] Time elapsed note: ${timeElapsedNote}`);
    console.log(`[${type.toUpperCase()} CALL] Conversation preview: ${conversationText.substring(0, 200)}...`);

    // 6) Call Gemini to get the "opener"
    console.log(`[${type.toUpperCase()} CALL] Calling Gemini for opener text`);
    
    // Get the appropriate opener system prompt based on call type
    let openerPromptTypeKey = type === 'goal' ? 'goalCallOpenerPrompt' : 
                     type === 'firstCall' ? 'newUserCallOpenerPrompt' : 
                     'callOpenerPrompt';
    
    console.log(`[${type.toUpperCase()} CALL] Fetching ${openerPromptTypeKey} system prompt`);
    // Fetch prompt data only, tools not needed for opener
    const { promptData: openerPromptData } = await getCoachPromptAndTools(coachName, openerPromptTypeKey);
    
    const openerSystemPrompt = openerPromptData?.SystemPrompt || 
      `You are ${coachName}, an AI transformational coach. You're starting a coaching call with a client. ${
        type === 'goal' ? 'They have a specific goal they want to work on.' : 
        type === 'firstCall' ? 'This is your first call with them.' : 
        'You\'re continuing your coaching relationship with them.'
      } Your job is to create a warm, engaging opening message that ${
        type === 'goal' ? 'acknowledges their goal' : 'welcomes them'
      } and sets a positive tone for the conversation. Be empathetic, curious, and focused on helping them achieve meaningful progress.

Please respond with JSON in this exact format:
{
  "reflection": "Your thinking about the client's situation and what would be most helpful",
  "final": "Your warm, engaging opening message for the call"
}`;
    
    console.log(`[${type.toUpperCase()} CALL] Opener system prompt fetched successfully`);
    
    const geminiPayload = {
      system_instruction: {
        parts: {
          text: openerSystemPrompt,
        },
      },
      contents: [
        {
          role: "user",
          parts: [
            {
              text: `<CLIENTS_PROFILE>${profileText || ""}</CLIENTS_PROFILE>
<PREVIOUS_CONVERSATION>${conversationText}</PREVIOUS_CONVERSATION>
${type !== 'firstCall' ? timeElapsedNote : ''}
${type === 'goal' ? `<CURRENT_GOAL>${goalInput || ""}</CURRENT_GOAL>` : ''}
[Call beginning]`,
            },
          ],
        },
      ],
      generationConfig: {
        temperature: 1.0,
      },
      safetySettings: [
        {
          category: "HARM_CATEGORY_DANGEROUS_CONTENT",
          threshold: "BLOCK_NONE",
        },
        {
          category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          threshold: "BLOCK_NONE",
        },
        {
          category: "HARM_CATEGORY_HATE_SPEECH",
          threshold: "BLOCK_NONE",
        },
        {
          category: "HARM_CATEGORY_HARASSMENT",
          threshold: "BLOCK_NONE",
        },
      ],
    };

    // Call Gemini with structured output
    const geminiResponse = await geminiRequest([geminiPayload], undefined, true);
    console.log(`[${type.toUpperCase()} CALL] Gemini response received`);
    
    // Parse the structured response
    let openerText = "";
    
    try {
      const data = await geminiResponse.json();
      console.log(`[${type.toUpperCase()} CALL] Gemini structured response:`, JSON.stringify(data, null, 2));
      
      // Check if we got structured output
      if (data.success === "true" && data.text1 && typeof data.text1 === 'object' && data.text1.final) {
        openerText = data.text1.final;
        console.log(`[${type.toUpperCase()} CALL] Successfully extracted from structured output:`, openerText);
        console.log(`[${type.toUpperCase()} CALL] Reflection:`, data.text1.reflection);
      } else if (data.success === "true" && data.text1 && typeof data.text1 === 'string') {
        // Fallback: try to parse string response if structured parsing failed
        console.log(`[${type.toUpperCase()} CALL] Got string response, trying JSON parse`);
        try {
          const parsed = JSON.parse(data.text1);
          if (parsed.final) {
            openerText = parsed.final;
            console.log(`[${type.toUpperCase()} CALL] Successfully parsed JSON from string:`, openerText);
          } else {
            throw new Error("No 'final' field in parsed JSON");
          }
        } catch (parseError) {
          console.log(`[${type.toUpperCase()} CALL] JSON parse failed, using raw text:`, data.text1);
          openerText = data.text1;
        }
      } else {
        console.log(`[${type.toUpperCase()} CALL] Unexpected response format, using fallback`);
        openerText = `Hello, let's begin our call. ${type === 'goal' ? 'I understand you have a goal you\'d like to work on today.' : 'I\'m looking forward to our conversation.'}`;
      }
    } catch (error) {
      console.error(`[${type.toUpperCase()} CALL] Error parsing Gemini response:`, error);
      openerText = `Hello, let's begin our call. ${type === 'goal' ? 'I understand you have a goal you\'d like to work on today.' : 'I\'m looking forward to our conversation.'}`;
    }
    
    // Replace or escape newlines/quotes
    const finalOpenerText = openerText.replace(/\n/g, " ").replace(/"/g, "'").trim();
    console.log(`[${type.toUpperCase()} CALL] Final opener text after cleaning:`, finalOpenerText);

    // 7) Fetch system prompt for the coach based on call type
    let promptTypeKey = type === 'goal' ? 'goalCallPrompt' : 
                    type === 'firstCall' ? 'callPrompt' : 
                    'callPrompt';
    
    console.log(`[${type.toUpperCase()} CALL] Fetching coach prompt and tools config (${promptTypeKey})`);
    const { promptData, toolsConfig } = await getCoachPromptAndTools(coachName, promptTypeKey);
    console.log(`[${type.toUpperCase()} CALL] Coach system prompt fetched successfully`);
    console.log(`[${type.toUpperCase()} CALL] Tools config:`, toolsConfig); // Log fetched tools

    // 8) Build final system prompt by inserting placeholders
    console.log(`[${type.toUpperCase()} CALL] Building final system prompt`);
    // Use a fallback default prompt if SystemPrompt is undefined or empty
    let defaultPrompt = `You are ${coachName}, an AI coach. You're having a call with a client`;
    if (type === 'goal') {
      defaultPrompt += ` about their goal: "$CURRENT_GOAL"`;
    } else if (type === 'firstCall') {
      defaultPrompt += `. This is your first call with them.`;
    } else {
      defaultPrompt += `. This is a continuation of your ongoing coaching relationship.`;
    }
    defaultPrompt += ` Be supportive and help them achieve their goals.`;
    
    let finalSystemPrompt = promptData?.SystemPrompt || defaultPrompt;
    
    try {
      // Replace placeholders
      finalSystemPrompt = finalSystemPrompt
        .replace("{{CLIENT_PROFILE}}", profileText || "")
        .replace("{{CURRENT_GOAL}}", goalInput || "")
        .concat(`\n<PREVIOUS_CONVERSATION>${conversationText}</PREVIOUS_CONVERSATION>${timeElapsedNote}[Call beginning]`);
      console.log(`[${type.toUpperCase()} CALL] Final system prompt built successfully`);
    } catch (error) {
      console.error(`[${type.toUpperCase()} CALL] Error replacing placeholders:`, error);
      // Fallback to a simpler prompt if replacements fail
      finalSystemPrompt = defaultPrompt;
      finalSystemPrompt += `\n<PREVIOUS_CONVERSATION>${conversationText}</PREVIOUS_CONVERSATION>${timeElapsedNote}[Call beginning]`;
    }

    // (PAUSE HERE) - Initialize vapiFunctions and vapiMetadata
    const vapiFunctions: any[] = [
      { type: "endCall" } // Default endCall tool
    ];
    // Initialize metadata with channelId, add more tool-specific metadata below
    const vapiMetadata: Record<string, any> = { 
      channelId: channelId, 
      coachName: coachName,
      maxCallDuration: maxCallDuration
    };

    // Iterate through the fetched toolsConfig array
    if (toolsConfig && Array.isArray(toolsConfig)) {
      for (const tool of toolsConfig) {
        if (!tool || !tool.name) continue; // Skip invalid tool configs

        const toolSchema = TOOL_VAPI_CONFIGS[tool.name];
        if (toolSchema) {
          console.log(`[${type.toUpperCase()} CALL] Enabling tool: ${tool.name}`);
          vapiFunctions.push(toolSchema);

          // Add tool-specific metadata if needed
          if (tool.name === 'fetch_knowledge' && tool.kb_identifier) {
            console.log(`[${type.toUpperCase()} CALL] Adding kb_identifier to metadata: ${tool.kb_identifier}`);
            vapiMetadata.kb_identifier = tool.kb_identifier;
          }
          // Add other tool metadata handling here if necessary
        }
      }
    }

    // 9) Construct the final config object for VAPI
    console.log(`[${type.toUpperCase()} CALL] Building final VAPI config`);
    const config = {
      name: coachName,
      model: {
        model: modelName,
        messages: [
          {
            role: "system",
            content: finalSystemPrompt
          }
        ],
        provider: modelProvider,
        tools: vapiFunctions, // Assign processed tool functions
        maxTokens: 482,
        temperature: 0.75,
        emotionRecognitionEnabled: false
      },
      voice: {
        voiceId: voiceId,
        provider: "11labs",
        stability: stability,
        similarityBoost: similarityBoost,
        model: model,
        speed: speed,
        style: style,
        useSpeakerBoost: useSpeakerBoost,
        optimizeStreamingLatency: optimizeStreamingLatency
      },
      startSpeakingPlan: {
        smartEndpointingPlan: {
          provider: "livekit",
          waitFunction: "100 + 3400/(1+exp(-40*(x-0.8))) + 2500/(1+exp(-200*(x-0.98))) + 24000/(1+exp(-1000*(x-0.995)))"
        }
      },
      metadata: vapiMetadata, // Assign processed metadata
      serverUrl: Reframe.env.APP_URL + "/handle-call-end",
      transcriber: transcriberConfig,
      artifactPlan: {
        recordingEnabled: enableRecording
      },
      firstMessage: finalOpenerText,
      firstMessageInterruptionsEnabled: true,
      hipaaEnabled: false,
      summaryPrompt: "<string>",
      clientMessages: [
        "transcript",
        "hang",
        "tool-calls",
        "speech-update",
        "metadata",
        "conversation-update"
      ],
      endCallMessage: "Pleasure to speak with you. Till next time",
      endCallPhrases: [
        "bye for now. Speak soon",
        "talk soon",
        "goodbye",
        "bye",
        "good night"
      ],
      serverMessages: [
        "end-of-call-report",
        "hang"
      ],
      backgroundSound: backgroundSound,
      serverUrlSecret: "<string>",
      firstMessageMode: "assistant-speaks-first",
      recordingEnabled: enableRecording,
      maxDurationSeconds: 20 + (maxCallDuration || 5) * 60,
      responseDelaySeconds: 0.5,
      backchannelingEnabled: false,
      silenceTimeoutSeconds: 300,
      endCallFunctionEnabled: false,
      llmRequestDelaySeconds: 0.1,
      dialKeypadFunctionEnabled: false,
      voicemailDetectionEnabled: false,
      modelOutputInMessagesEnabled: true,
      numWordsToInterruptAssistant: 1,
      customerJoinTimeoutSeconds: 30
    };
    console.log(`[${type.toUpperCase()} CALL] Final VAPI config built successfully`);
    // console.log(`[${type.toUpperCase()} CALL] Config:`, JSON.stringify(config, null, 2));
    // 10) Return the config
    return config;
  } catch (error) {
    console.error(`[${type.toUpperCase()} CALL] Error building ${type} call assistant config:`, error);
    throw error;
  }
}

/**
 * Builds a configuration for a goal-oriented coaching call with Claude LLM
 *
 * @param channelId string - The user's channel ID
 * @param goalInput string | undefined - The user's stated goal for the call
 * @param maxCallDuration number - Maximum call duration in seconds (default: 1500)
 * @param coachName string - The name of the coach (default: "Kokoro")
 * @returns {Promise<any>} The JSON config that the route can directly return to the client
 */
export async function buildGoalCallAssistantConfig(
  channelId: string,
  goalInput?: string,
  maxCallDuration: number = 1500,
  coachName: string = "Kokoro"
): Promise<any> {
  // For backward compatibility, delegate to the new generalized function
  return buildCallAssistantConfig(
    channelId,
    'goal',
    goalInput,
    maxCallDuration,
    coachName
  );
} 

export const trackCall = async ( channelId: string, callId: string, controlUrl: string, assistantId: string, coachName: string = "Kokoro" ) => {
  try {
    console.log(`[CALL TRACKING] Call started - ID: ${callId}, User: ${channelId}, Coach: ${coachName}`);
    
    // Get coach multiplier for essence calculations
    const mult = await fetchMultiplier(coachName);
    console.log(`[CALL TRACKING] Coach ${coachName} multiplier: ${mult}`);
    
    // Get user's subscription and usage information
    const userData = await getUserData(channelId);
    const userPlanType = (userData.subscription?.planId.split('_')[0] ?? 'free') as PlanType;
    
    // Fetch user's timezone to inform the assistant
    const user = await db
      .selectFrom("user")
      .select(["timezone", "name"]) // Fetch name here too to avoid second query later
      .where("channelId", "=", Number(channelId))
      .executeTakeFirst();

    if (user?.timezone) {
      try {
        const now = new Date();
        // Get time string like "14:35" in user's local time
        const userTime = now.toLocaleTimeString("en-GB", { // en-GB uses 24-hour format HH:MM
          timeZone: user.timezone,
          hour: '2-digit',
          minute: '2-digit',
          hour12: false // Ensure 24-hour format
        });
        console.log(`[CALL TRACKING] User's local time: ${userTime} in ${user.timezone}`);

        // Send a message to the assistant about the client's local time
        await fetch(controlUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            type: 'add-message',
            message: {
              // Using 'user' role to mimic a system note injected into the conversation context
              role: 'assistant',
              content: `[Current time is ${userTime}]`
            },
            triggerResponseEnabled: false // Don't make the assistant respond to this
          })
        });
        console.log(`[CALL TRACKING] Sent local time notification to assistant.`);
      } catch (timeError) {
         console.error(`[CALL TRACKING] Error getting/sending local time:`, timeError);
         // Continue tracking even if time notification fails
      }
    } else {
       console.log(`[CALL TRACKING] User timezone not found for ${channelId}. Skipping time notification.`);
    }

    // Get plan limits
    const maxCallDuration = PLANS[userPlanType].limits.maxCallDuration;
    const initialRemainingBalance = await getTotalEssenceBalance(channelId);
    const initialEffectiveMins = initialRemainingBalance / mult;  // Adjust for multiplier
    
    // console.log(`[CALL TRACKING] User plan: ${userPlanType}, Max duration: ${maxCallDuration}min, Monthly limit: ${monthlyCallMins}min`);
    // console.log(`[CALL TRACKING] Current usage: ${userData.callMinsThisPeriod}/${monthlyCallMins}min this period`);
    
    // Store call info in a temporary Map (this would be DB in production)
    const callData = {
      channelId,
      callId, 
      controlUrl,
      assistantId,
      startTime: Date.now(),
      hasWarned: false,
      checkCount: 0 // Add counter for periodic updates
    };
    
    // Set up a timer to check call status and enforce limits
    const intervalId = setInterval(async () => {
      try {
        // Check if call is still active
        const callStatusResponse = await fetch(`https://api.vapi.ai/call/${callId}`, {
          headers: {
            'Authorization': `Bearer 126850ad-cc93-49cf-9c45-e833138d1f39`,
          }
        });
        
        if (!callStatusResponse.ok) {
          console.log(`[CALL TRACKING] Error checking call status: ${callStatusResponse.statusText}`);
          clearInterval(intervalId);
          return;
        }
        
        const callStatus = await callStatusResponse.json();
        console.log(`[CALL TRACKING] Call ${callId} status: ${callStatus.status}`);
        
        // If call ended, clean up and stop checking
        if (callStatus.status === 'ended') {
          console.log(`[CALL TRACKING] Call ${callId} has ended, stopping tracker`);
          clearInterval(intervalId);
          
          // // Calculate actual call duration in minutes
          // const callDurationMs = Date.now() - callData.startTime;
          // const callDurationMin = callDurationMs / (1000 * 60);
          
          // // Cap call duration at maxCallDuration if needed
          // const finalDurationMin = Math.min(callDurationMin, maxCallDuration);
          // console.log(`[CALL TRACKING] Call completed: ${finalDurationMin.toFixed(2)} minutes`);
          
          // // Update user's call minutes
          // await incrementCallMinutes(channelId, finalDurationMin);
          return;
        }
        
        // Continue tracking active call
        if (callStatus.status === 'in-progress') {
          // Calculate current duration
          const currentDurationMs = Date.now() - callData.startTime;
          const currentDurationMin = currentDurationMs / (1000 * 60);
          console.log(`[CALL TRACKING] Call in progress: ${currentDurationMin.toFixed(2)} minutes`);
          
          const checkpointMinutes = 5;
          // Check if user has more than checkpointMinutes remaining in their monthly allowance
          const hasMoreThanCheckpointMinutes = initialEffectiveMins > checkpointMinutes;
          
          // Check if approaching monthly limit (remaining ≤ 5 minutes)
          const totalBalanceNow = await getTotalEssenceBalance(channelId);
          const baseCostSoFar = getActionCost("call", currentDurationMin);
          const currentEssenceCost = essenceCost(baseCostSoFar, mult);
          const remainingEssence = totalBalanceNow - currentEssenceCost;
          const effectiveMinsLeft = remainingEssence / mult;
          
          if (effectiveMinsLeft <= checkpointMinutes && !callData.hasWarned && hasMoreThanCheckpointMinutes) {
            console.log(`[CALL TRACKING] User approaching monthly limit, sending warning`);
            
            const userName = user?.name?.split(' ')[0] || '';

            // Send message to user to warn them
            await fetch(controlUrl, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                type: 'say',
                content: `${userName}, there's five minutes left in your monthly call allowance. How would you like to use it?`
              })
            });

            /* Send a system message to the assistant about the remaining time
            await fetch(controlUrl, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                type: 'add-message',
                message: {
                  role: 'system',
                  // Inform the assistant about the time constraint
                  content: `[SYSTEM NOTE: The client has approximately 5 minutes remaining in their monthly call allowance. Please guide the conversation towards a wrap-up.]`
                },
                // Don't trigger an immediate response from the assistant based on this system note
                triggerResponseEnabled: false 
              })
            });
            */
            
            // Mark that we've sent the warning
            callData.hasWarned = true;
          }

          // Increment check counter
          callData.checkCount++;

          // Periodically add elapsed time as a system message (Example: every 2 minutes)
          // Calculate current duration in minutes and seconds for the message
          const currentDurationTotalSeconds = Math.floor(currentDurationMs / 1000);
          const elapsedMinutes = Math.floor(currentDurationTotalSeconds / 60);
          const elapsedSeconds = currentDurationTotalSeconds % 60;

          // Send update every 6 checks (6 * 10s interval = 60s = 1 minute)
          // Ensure the call has been going for at least 30 seconds to avoid immediate messages
          if (currentDurationTotalSeconds > 30 && callData.checkCount % 6 === 0) { 
            console.log(`[CALL TRACKING] Adding elapsed time system message: ${elapsedMinutes}m ${elapsedSeconds}s`);
            await fetch(controlUrl, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                type: 'add-message',
                message: {
                  role: 'user',
                  content: `[Call time: ${elapsedMinutes} minutes (for info only, do not read this out)]`
                },
                // No response needed for this informational message
                triggerResponseEnabled: false 
              })
            });
          }
          
          // Check if reached monthly limit - 20 seconds remaining
          const cutoffBaseCost = getActionCost("call", 0.33);
          if (remainingEssence <= essenceCost(cutoffBaseCost, mult)) {
            console.log(`[CALL TRACKING] User reached monthly limit, ending call`);
            
            // Get user info for personalized message
            const userName = user?.name?.split(' ')[0] || '';
            
            // End call with explanation
            await fetch(controlUrl, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                type: 'say',
                content: `${userName}, we're out of time for this month. There's more I'd love to share and explore if you choose to upgrade, or we can continue via messages. Let's speak soon.`,
                endCallAfterSpoken: true
              })
            });
            
            clearInterval(intervalId);
            return;
          }
          
          // Check if reached max call duration
          // TEMP: Set max call duration to 1 min for testing
          // const maxCallDuration = 0.5;
          if (currentDurationMin >= maxCallDuration) {
            console.log(`[CALL TRACKING] Call reached max duration (${maxCallDuration}min), ending call`);
            
            // Get user info for personalized message
            const userName = user?.name?.split(' ')[0] || '';
            
            // End call with explanation
            await fetch(controlUrl, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                type: 'say',
                content: `${userName} <break time="1.0s" /> we're reaching our max call time so we will be cut off, but you can call back to continue.`,
                endCallAfterSpoken: true
              })
            });
            
            clearInterval(intervalId);
            return;
          }
        }
      } catch (error) {
        console.error(`[CALL TRACKING] Error in interval check:`, error);
        clearInterval(intervalId);
      }
    }, 10000); // Check every 10 seconds
    
    return { success: true };
  } catch (error) {
    console.error(`[CALL TRACKING] Error:`, error);
    return { error: error.message };
  }
}

/**
 * Builds a configuration for a guided coaching call with Claude LLM
 * Uses a dynamically generated session plan based on user goal and profile
 *
 * @param channelId string - The user's channel ID
 * @param actualGoal string - The user's stated goal without the <guided> tag
 * @param sessionPlan string - The pre-generated session plan for this call
 * @param maxCallDuration number - Maximum call duration in seconds (default: 1500)
 * @param coachName string - The name of the coach (default: "Kokoro")
 * @returns {Promise<any>} The JSON config that the route can directly return to the client
 */
export async function buildGuidedCallAssistantConfig(
  channelId: string,
  actualGoal: string,
  sessionPlan: string,
  maxCallDuration: number = 1500,
  coachName: string = "Kokoro"
): Promise<any> {
  console.log("[GUIDED CALL] Starting to build guided call assistant config");
  console.log("[GUIDED CALL] Params:", { channelId, actualGoal, maxCallDuration, coachName });
  console.log("[GUIDED CALL] Session plan length:", sessionPlan?.length || 0);

  try {
    // Fetch user email to check for superuser status
    console.log("[GUIDED CALL] Fetching user email for superuser check");
    const user = await db
      .selectFrom("user")
      .select("email")
      .where("channelId", "=", Number(channelId)) // Ensure channelId is a number if needed by DB schema
      .executeTakeFirst();
    const userEmail = user?.email;
    const isSuperuser = userEmail && Array.isArray(SUPERUSER_EMAILS) ? SUPERUSER_EMAILS.includes(userEmail) : false;
    const enableRecording = isSuperuser; // Set recording based on superuser status
    console.log(`[GUIDED CALL] User email: ${userEmail}, Is Superuser: ${isSuperuser}, Recording Enabled: ${enableRecording}`);

    // Most of this function is similar to buildCallAssistantConfig, but with session plan integration
    
    // Get coach data including user's selected voice preference
    console.log("[GUIDED CALL] Fetching coach data with user voice preference");
    const coach = await getCoachAction(coachName, channelId);
    
    if (!coach) {
      console.error("[GUIDED CALL] Coach not found:", coachName);
      throw new Error(`Coach not found: ${coachName}`);
    }
    
    // Set up voice config using the selected voice or default
    let voiceId = "OPp59xqerF9pc477FJM8"; // Default fallback
    let stability = 0.35;
    let similarityBoost = 0.75;
    let selectedVoiceName = "KokoroMale"; // Default voice name
    let model = "eleven_turbo_v2"; // Default model
    let speed = 1.0; // Default speed
    let style = 0.0; // Default style
    let useSpeakerBoost = false; // Default speaker boost
    let optimizeStreamingLatency = 3; // Default streaming latency
    
    if (coach.selectedVoice) {
      // Using the user's selected voice
      voiceId = coach.selectedVoice.voice_id;
      stability = coach.selectedVoice.voice_settings?.stability || stability;
      similarityBoost = coach.selectedVoice.voice_settings?.similarity_boost || similarityBoost;
      selectedVoiceName = coach.selectedVoice.voice_name;
      model = coach.selectedVoice.model || model;
      speed = coach.selectedVoice.speed || speed;
      style = coach.selectedVoice.voice_settings?.style || style;
      useSpeakerBoost = coach.selectedVoice.voice_settings?.useSpeakerBoost ?? useSpeakerBoost; // Use nullish coalescing for boolean
      optimizeStreamingLatency = coach.selectedVoice.voice_settings?.optimizeStreamingLatency || optimizeStreamingLatency;
    } else if (coach.metadata) {
      // No user selection, use first voice in metadata if available
      const metadata = typeof coach.metadata === 'string' 
        ? JSON.parse(coach.metadata) 
        : coach.metadata;
      
      if (metadata.voices && metadata.voices.length > 0) {
        const defaultVoice = metadata.voices[0];
        voiceId = defaultVoice.voice_id;
        stability = defaultVoice.voice_settings?.stability || stability;
        similarityBoost = defaultVoice.voice_settings?.similarity_boost || similarityBoost;
        selectedVoiceName = defaultVoice.voice_name;
        model = defaultVoice.model || model;
        speed = defaultVoice.speed || speed;
        style = defaultVoice.voice_settings?.style || style;
        useSpeakerBoost = defaultVoice.voice_settings?.useSpeakerBoost ?? useSpeakerBoost; // Use nullish coalescing for boolean
        optimizeStreamingLatency = defaultVoice.voice_settings?.optimizeStreamingLatency || optimizeStreamingLatency;
      }
    }
    
    console.log(`[GUIDED CALL] Using voice: ${selectedVoiceName} (${voiceId}), model: ${model}, speed: ${speed}, style: ${style}, speakerBoost: ${useSpeakerBoost}, streamingLatency: ${optimizeStreamingLatency}`);

    // Set default model details
    let modelProvider = "anthropic";
    let modelName = "claude-3-7-sonnet-20250219";

    // Check metadata for model override
    if (coach.metadata) {
      const metadata = typeof coach.metadata === 'string' 
        ? JSON.parse(coach.metadata) 
        : coach.metadata;
      
      if (metadata.model && Array.isArray(metadata.model) && metadata.model.length > 0) {
        const modelConfig = metadata.model[0];
        modelProvider = modelConfig.provider || modelProvider;
        modelName = modelConfig.model || modelName;
        console.log(`[GUIDED CALL] Overriding model with metadata: Provider=${modelProvider}, Model=${modelName}`);
      }
    }

    // Set default background sound and check metadata for override
    let backgroundSound = "off";
    if (coach.metadata) {
      const metadata = typeof coach.metadata === 'string' 
        ? JSON.parse(coach.metadata) 
        : coach.metadata;
      
      if (metadata.backgroundSound) {
        backgroundSound = metadata.backgroundSound;
        console.log(`[GUIDED CALL] Using custom background sound: ${backgroundSound}`);
      }
    }

    // Set default transcriber config and check metadata for override
    let transcriberConfig: any = {
      model: "nova-3",
      language: "en",
      provider: "deepgram",
      endpointing: null
    };
    
    if (coach.metadata) {
      const metadata = typeof coach.metadata === 'string' 
        ? JSON.parse(coach.metadata) 
        : coach.metadata;
      
      if (metadata.transcriber) {
        // Override with metadata transcriber config
        transcriberConfig = {
          model: metadata.transcriber.model || transcriberConfig.model,
          language: metadata.transcriber.language || transcriberConfig.language,
          provider: metadata.transcriber.provider || transcriberConfig.provider,
          endpointing: metadata.transcriber.endpointing !== undefined ? metadata.transcriber.endpointing : transcriberConfig.endpointing,
          // Include keywords if present in metadata
          ...(metadata.transcriber.keywords ? { keywords: metadata.transcriber.keywords } : {})
        };
        console.log(`[GUIDED CALL] Using custom transcriber config:`, transcriberConfig);
      }
    }

    // 1) Fetch user profile text from Airtable
    console.log("[GUIDED CALL] Fetching user profile");
    const { profileText } = await getProfileText(channelId);
    console.log("[GUIDED CALL] Profile fetched successfully");

    // 3) Fetch user's conversation & decrypt
    console.log("[GUIDED CALL] Fetching and decrypting messages");
    const messages = await decryptMessages(channelId, true, coachName, 25, ["message", "proactive_message"]);
    console.log("[GUIDED CALL] Messages fetched and decrypted successfully");

    // 4 & 5) Use the new prepMessageHistoryAction to prepare conversation text and time elapsed note
    console.log("[GUIDED CALL] Preparing message history");
    
    // Log message details for testing
    console.log(`[GUIDED CALL] Retrieved ${messages.length} messages for context`);
    if (messages.length > 0) {
      const messageTypeCounts = messages.reduce((acc: any, msg: any) => {
        acc[msg.Type] = (acc[msg.Type] || 0) + 1;
        return acc;
      }, {});
      console.log(`[GUIDED CALL] Message type breakdown:`, messageTypeCounts);
      console.log(`[GUIDED CALL] Sample messages:`, messages.slice(0, 3).map((m: any) => ({ Type: m.Type, Sender: m.Sender, Content: m.Content.substring(0, 50) + '...' })));
    }
    
    const { conversationText, timeElapsedNote } = prepMessageHistoryAction(messages);
    console.log("[GUIDED CALL] Message history prepared");
    console.log(`[GUIDED CALL] Conversation text length: ${conversationText.length} characters`);
    console.log(`[GUIDED CALL] Time elapsed note: ${timeElapsedNote}`);
    console.log(`[GUIDED CALL] Conversation preview: ${conversationText.substring(0, 200)}...`);

    // 6) Call Gemini to get the "opener" using the session plan
    console.log("[GUIDED CALL] Calling Gemini for opener text with session plan");
    
    // Get the guided call opener system prompt 
    console.log("[GUIDED CALL] Fetching guidedCallOpenerPrompt system prompt");
    // First try to get a specific guided opener prompt, fall back to goal opener if not found
    let openerPromptData; // Renamed variable
    try {
      // Fetch prompt data only, tools not needed for opener
      const openerResult = await getCoachPromptAndTools(coachName, "guidedCallOpenerPrompt");
      openerPromptData = openerResult.promptData;
    } catch (error) {
      console.log("[GUIDED CALL] No specific guided opener prompt found, using goal opener");
      // Fetch prompt data only, tools not needed for opener
      const openerResult = await getCoachPromptAndTools(coachName, "goalCallOpenerPrompt");
      openerPromptData = openerResult.promptData;
    }
    
    const openerSystemPrompt = openerPromptData?.SystemPrompt || 
      `You are ${coachName}, an AI transformational coach. You're starting a guided coaching call with a client. 
      They have a specific goal they want to work on, and you have a session plan to help guide the conversation.
      Your job is to create a warm, engaging opening message that acknowledges their goal and sets a positive tone for the conversation.
      Be empathetic, curious, and focused on helping them achieve meaningful progress following the session plan.`;
    
    console.log("[GUIDED CALL] Opener system prompt fetched successfully");
    
    const geminiPayload = {
      system_instruction: {
        parts: {
          text: openerSystemPrompt,
        },
      },
      contents: [
        {
          role: "user",
          parts: [
            {
              text: `<CLIENTS_PROFILE>${profileText || ""}</CLIENTS_PROFILE>
<PREVIOUS_CONVERSATION>${conversationText}</PREVIOUS_CONVERSATION>
${timeElapsedNote}
<CURRENT_GOAL>${actualGoal || ""}</CURRENT_GOAL>
<SESSION_PLAN>${sessionPlan || ""}</SESSION_PLAN>
[Call beginning]`,
            },
          ],
        },
      ],
      generationConfig: {
        temperature: 1.0,
      },
      safetySettings: [
        {
          category: "HARM_CATEGORY_DANGEROUS_CONTENT",
          threshold: "BLOCK_NONE",
        },
        {
          category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          threshold: "BLOCK_NONE",
        },
        {
          category: "HARM_CATEGORY_HATE_SPEECH",
          threshold: "BLOCK_NONE",
        },
        {
          category: "HARM_CATEGORY_HARASSMENT",
          threshold: "BLOCK_NONE",
        },
      ],
    };

    // Call Gemini
    const geminiResponse = await geminiRequest([geminiPayload]);
    console.log("[GUIDED CALL] Gemini response received");
    
    // Parse the response
    let openerText = "";
    
    try {
      // geminiRequest always returns a Response object
      const data = await geminiResponse.json();
      console.log("[GUIDED CALL] Gemini raw response data:", JSON.stringify(data, null, 2));
      
      // If successful and we have text, use it
      if (data.success === "true" && data.text1) {
        const rawText = data.text1;
        console.log("[GUIDED CALL] Gemini raw text:", rawText);
        
        // Extract text between <final> tags if present (for all call types)
        if (rawText.includes('<final>') && rawText.includes('</final>')) {
          console.log("[GUIDED CALL] Found <final> tags, attempting regex extraction");
          const matches = rawText.match(/<final>(.*?)<\/final>/s);
          if (matches && matches[1]) {
            openerText = matches[1].trim();
            console.log("[GUIDED CALL] Successfully extracted from <final> tags:", openerText);
          } else {
            console.log("[GUIDED CALL] Regex extraction failed, falling back to tag cleaning");
            // If regex fails but tags exist, fall through to cleaning logic
            let cleanedText = rawText;
            
            // Remove reflection tags and their content
            cleanedText = cleanedText.replace(/<reflection>.*?<\/reflection>/gs, '');
            
            // Remove thinking tags and their content
            cleanedText = cleanedText.replace(/<thinking>.*?<\/thinking>/gs, '');
            
            // Remove any other common internal reasoning tags
            cleanedText = cleanedText.replace(/<analysis>.*?<\/analysis>/gs, '');
            cleanedText = cleanedText.replace(/<reasoning>.*?<\/reasoning>/gs, '');
            
            // Clean up extra whitespace
            openerText = cleanedText.trim();
            console.log("[GUIDED CALL] Cleaned text result:", openerText);
          }
        } else {
          console.log("[GUIDED CALL] No <final> tags found, cleaning raw text");
          // If no <final> tags, remove any reflection/thinking tags and use the remaining text
          let cleanedText = rawText;
          
          // Remove reflection tags and their content
          cleanedText = cleanedText.replace(/<reflection>.*?<\/reflection>/gs, '');
          
          // Remove thinking tags and their content
          cleanedText = cleanedText.replace(/<thinking>.*?<\/thinking>/gs, '');
          
          // Remove any other common internal reasoning tags
          cleanedText = cleanedText.replace(/<analysis>.*?<\/analysis>/gs, '');
          cleanedText = cleanedText.replace(/<reasoning>.*?<\/reasoning>/gs, '');
          
          // Clean up extra whitespace
          openerText = cleanedText.trim();
          console.log("[GUIDED CALL] Cleaned text result:", openerText);
        }
      } else {
        console.log("[GUIDED CALL] Gemini response not successful or missing text1, using fallback");
        // Fallback if no valid response
        openerText = `Hello, let's begin our guided session. I understand your goal is: ${actualGoal}. I have a plan to help us make progress on this today.`;
      }
    } catch (error) {
      console.error("[GUIDED CALL] Error parsing Gemini response:", error);
      openerText = `Hello, let's begin our guided session. I understand your goal is: ${actualGoal}. I have a plan to help us make progress on this today.`;
    }
    
    // Replace or escape newlines/quotes
    const finalOpenerText = openerText.replace(/\n/g, " ").replace(/"/g, "'").trim();
    console.log("[GUIDED CALL] Final opener text after cleaning:", finalOpenerText);

    // 7) Fetch system prompt specifically for guided calls
    console.log("[GUIDED CALL] Fetching system prompt");
    
    // First try to get a specific guided call prompt, fall back to goal call prompt if not found
    let promptData; // Renamed variable
    let toolsConfig; // Added variable
    let promptTypeKey = "guidedCallPrompt"; // Define the key
    try {
      const result = await getCoachPromptAndTools(coachName, promptTypeKey);
      promptData = result.promptData;
      toolsConfig = result.toolsConfig;
    } catch (error) {
      console.log("[GUIDED CALL] No specific guided call prompt found, using goal call prompt");
      promptTypeKey = "goalCallPrompt"; // Update the key for fallback
      const result = await getCoachPromptAndTools(coachName, promptTypeKey);
      promptData = result.promptData;
      toolsConfig = result.toolsConfig;
    }
    
    const { SystemPrompt: callPrompt } = promptData || {}; // Use promptData
    console.log("[GUIDED CALL] System prompt fetched successfully");
    console.log("[GUIDED CALL] Tools config:", toolsConfig); // Log fetched tools

    // 8) Replace placeholders in the system prompt
    console.log("[GUIDED CALL] Replacing placeholders in system prompt");
    
    // Use a fallback default prompt if SystemPrompt is undefined or empty
    let defaultPrompt = `You are ${coachName}, an AI coach. You're having a guided call with a client about their goal: "${actualGoal}".
You have a session plan to follow: ${sessionPlan}
Be supportive and help them achieve their goals.`;
    
    let finalSystemPrompt = callPrompt || defaultPrompt;
    
    // Replace session plan placeholder if it exists
    if (finalSystemPrompt.includes("{{SESSION_PLAN}}")) {
      finalSystemPrompt = finalSystemPrompt.replace("{{SESSION_PLAN}}", sessionPlan || "");
    }
    
    // Replace standard placeholders
    finalSystemPrompt = finalSystemPrompt
      .replace("{{CLIENT_PROFILE}}", profileText || "")
      .replace("{{CURRENT_GOAL}}", actualGoal || "");
    
    // Add conversation history and time elapsed note
    finalSystemPrompt = finalSystemPrompt.concat(`\n<PREVIOUS_CONVERSATION>${conversationText}</PREVIOUS_CONVERSATION>${timeElapsedNote}[Call beginning]`);
      
    console.log("[GUIDED CALL] Placeholders replaced");

    // (PAUSE HERE) - Initialize vapiFunctions and vapiMetadata
    const vapiFunctions: any[] = [
      { type: "endCall" } // Default endCall tool
    ];
    // Initialize metadata with channelId, add more tool-specific metadata below
    const vapiMetadata: Record<string, any> = { 
      channelId: channelId, 
      coachName: coachName, // Pass coachName in metadata
      maxCallDuration: maxCallDuration
    };

    // Iterate through the fetched toolsConfig array
    if (toolsConfig && Array.isArray(toolsConfig)) {
      for (const tool of toolsConfig) {
        if (!tool || !tool.name) continue; // Skip invalid tool configs

        const toolSchema = TOOL_VAPI_CONFIGS[tool.name];
        if (toolSchema) {
          console.log(`[GUIDED CALL] Enabling tool: ${tool.name}`);
          vapiFunctions.push(toolSchema);

          // Add tool-specific metadata if needed
          if (tool.name === 'fetch_knowledge' && tool.kb_identifier) {
            console.log(`[GUIDED CALL] Adding kb_identifier to metadata: ${tool.kb_identifier}`);
            vapiMetadata.kb_identifier = tool.kb_identifier;
          }
          // Add other tool metadata handling here if necessary
        }
      }
    }

    // 9) Build the final VAPI configuration
    console.log("[GUIDED CALL] Building VAPI configuration");
    
    const config = {
      name: coachName,
      model: {
        model: modelName,
        messages: [
          {
            role: "system",
            content: finalSystemPrompt
          }
        ],
        provider: modelProvider,
        tools: vapiFunctions, // Assign processed tool functions
        maxTokens: 482,
        temperature: 0.75,
        emotionRecognitionEnabled: false
      },
      voice: {
        voiceId: voiceId,
        provider: "11labs",
        stability: stability,
        similarityBoost: similarityBoost,
        model: model,
        speed: speed,
        style: style,
        useSpeakerBoost: useSpeakerBoost,
        optimizeStreamingLatency: optimizeStreamingLatency
      },
      startSpeakingPlan: {
        smartEndpointingPlan: {
          provider: "livekit",
          waitFunction: "100 + 3400/(1+exp(-40*(x-0.8))) + 2500/(1+exp(-200*(x-0.98))) + 24000/(1+exp(-1000*(x-0.995)))"
        }
      },
      metadata: vapiMetadata, // Assign processed metadata
      serverUrl: Reframe.env.APP_URL + "/handle-call-end",
      transcriber: transcriberConfig,
      artifactPlan: {
        recordingEnabled: enableRecording
      },
      firstMessage: finalOpenerText,
      firstMessageInterruptionsEnabled: true,
      hipaaEnabled: false,
      summaryPrompt: "<string>",
      clientMessages: [
        "transcript",
        "hang",
        "tool-calls",
        "speech-update",
        "metadata",
        "conversation-update"
      ],
      endCallMessage: "Pleasure to speak with you. Till next time",
      endCallPhrases: [
        "bye for now. Speak soon",
        "talk soon",
        "goodbye",
        "bye",
        "good night"
      ],
      serverMessages: [
        "end-of-call-report",
        "hang"
      ],
      backgroundSound: backgroundSound,
      serverUrlSecret: "<string>",
      firstMessageMode: "assistant-speaks-first",
      recordingEnabled: enableRecording,
      maxDurationSeconds: 20 + (maxCallDuration || 5) * 60,
      responseDelaySeconds: 0.5,
      backchannelingEnabled: false,
      silenceTimeoutSeconds: 300,
      endCallFunctionEnabled: false,
      llmRequestDelaySeconds: 0.1,
      dialKeypadFunctionEnabled: false,
      voicemailDetectionEnabled: false,
      modelOutputInMessagesEnabled: true,
      numWordsToInterruptAssistant: 1,
      customerJoinTimeoutSeconds: 30
    };
    
    console.log("[GUIDED CALL] VAPI configuration built successfully");
    return config;
  } catch (error) {
    console.error("[GUIDED CALL] Error building VAPI configuration:", error);
    throw error;
  }
}

export const getVapiConfig = async (
  channelId: string,
  // Legacy parameters (still needed for backward compatibility)
  isFirst: boolean,
  goalInput: string | null, // Nullable goalInput from legacy flow
  coachName: string = "Kokoro",
  // New parameter for card-based invocation
  cardContext?: CardContext // Make goalInput nullable
) => {
  if(!(await verifyUserPermission(channelId))) return null;
  try {
    // Get user's subscription plan and max duration (common to both flows)
    const userData = await getUserData(channelId);
    const userPlanType = (userData.subscription?.planId.split('_')[0] ?? 'free') as PlanType;
    const maxCallDuration = PLANS[userPlanType].limits.maxCallDuration;

    let vapiAssistantData;

    // === New Logic: Use cardContext if provided ===
    if (cardContext) {
      console.log(`[VAPI CONFIG - CARD] Using card context: Type=${cardContext.type}, OfferingID=${cardContext.coachOfferingId}`);

      switch (cardContext.type) {
        case 'GOAL':
          console.log("[VAPI CONFIG - CARD] Creating goal call configuration from card");
          // Ensure goalInput from context is used
          if (!cardContext.goalInput) {
             console.warn("[VAPI CONFIG - CARD] GOAL card type missing goalInput in context. Proceeding without specific goal.");
          }
          vapiAssistantData = await buildCallAssistantConfig(
            channelId,
            'goal',
            cardContext.goalInput, // Use goalInput specifically from context
            maxCallDuration,
            coachName
          );
          break;
        case 'EXPLORE':
          console.log("[VAPI CONFIG - CARD] Creating normal call configuration from card (EXPLORE)");
          vapiAssistantData = await buildCallAssistantConfig(
            channelId,
            'normal', // EXPLORE maps to a 'normal' call type
            undefined, // No specific goal input for explore
            maxCallDuration,
            coachName
          );
          break;
        case 'GUIDED_SESSION':
          console.log("[VAPI CONFIG - CARD] Creating guided call configuration from card");
          const sessionPlan = cardContext.data?.sessionPlan;
          // Use goalInput from UI first, fallback to data.goal, then to generic text
          const actualGoal = cardContext.goalInput || cardContext.data?.goal || "the guided session";

          if (!sessionPlan) {
            console.error("[VAPI CONFIG - CARD] Session plan missing in card context for GUIDED_SESSION, falling back to standard goal call with goal:", actualGoal);
            // Fallback: treat as a goal call with the available goal input
            vapiAssistantData = await buildCallAssistantConfig(
              channelId,
              'goal',
              actualGoal, // Use the determined actualGoal
              maxCallDuration,
              coachName
            );
          } else {
            console.log("[VAPI CONFIG - CARD] Using guided call config with session plan from card and goal:", actualGoal);
            vapiAssistantData = await buildGuidedCallAssistantConfig(
              channelId,
              actualGoal, // Pass the determined goal
              sessionPlan,
              maxCallDuration,
              coachName
            );
          }
          break;
        // Add cases for other card types ('CREATE_GUIDED', 'REFLECTION', 'MEDITATION') later
        default:
          console.warn(`[VAPI CONFIG - CARD] Unhandled card type: ${cardContext.type}. Falling back to normal call.`);
          vapiAssistantData = await buildCallAssistantConfig(
            channelId,
            'normal',
            undefined,
            maxCallDuration,
            coachName
          );
      }
    }
    // === Legacy Logic: Fallback if cardContext is NOT provided ===
    else {
      console.log(`[VAPI CONFIG - LEGACY] No card context provided, using legacy logic. isFirst: ${isFirst}, goalInput: ${goalInput}`);

      // Check if the legacy goal input contains the <guided> tag
      if (goalInput && goalInput.includes('<guided>')) {
        console.log("[VAPI - LEGACY] Guided call detected in goal input");

        // Extract the actual goal by removing the tag
        const actualGoal = goalInput.replace('<guided>', '').trim();
        console.log("[VAPI - LEGACY] Extracted actual goal:", actualGoal);

        // Generate the session plan (legacy way)
        console.log("[VAPI - LEGACY] Generating session plan");
        // Assuming generateGuidedSessionPlanAction still exists for legacy path
        const sessionPlan = await generateGuidedSessionPlanAction(channelId, actualGoal, coachName);

        if (!sessionPlan) {
          console.error("[VAPI - LEGACY] Failed to generate session plan, falling back to standard goal call");
          // Fallback: Treat as a standard goal call using the legacy function
          // Pass actualGoal extracted from the input
          vapiAssistantData = await buildCallAssistantConfig(channelId, 'goal', actualGoal, maxCallDuration, coachName);
        } else {
          // Use the guided call config with the session plan
          console.log("[VAPI - LEGACY] Using guided call config with session plan");
          vapiAssistantData = await buildGuidedCallAssistantConfig(
            channelId,
            actualGoal, // Pass the extracted goal
            sessionPlan,
            maxCallDuration,
            coachName
          );
        }
      } else {
        // Use the generalized function for other legacy call types
        if (goalInput) {
          console.log("[VAPI CONFIG - LEGACY] Creating goal call configuration");
          vapiAssistantData = await buildCallAssistantConfig(
            channelId,
            'goal',
            goalInput, // Use the direct goalInput
            maxCallDuration,
            coachName
          );
        } else if (isFirst) {
          console.log("[VAPI CONFIG - LEGACY] Creating first call configuration");
          vapiAssistantData = await buildCallAssistantConfig(
            channelId,
            'firstCall',
            undefined,
            maxCallDuration,
            coachName
          );
        } else {
          console.log("[VAPI CONFIG - LEGACY] Creating regular call configuration");
          vapiAssistantData = await buildCallAssistantConfig(
            channelId,
            'normal',
            undefined,
            maxCallDuration,
            coachName
          );
        }
      }
    } // End of legacy logic block

    // === Common Logic: Create VAPI Assistant ===
    if (!vapiAssistantData) {
      throw new Error("Failed to determine VAPI assistant configuration.");
    }

    console.log("[VAPI CREATE] Requesting assistant creation from VAPI...");
    // console.log("[VAPI CREATE] Request body:", JSON.stringify(vapiAssistantData, null, 2)); // Verbose logging if needed

    const vapiAssistantCreateResponse = await fetch('https://api.vapi.ai/assistant', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer 126850ad-cc93-49cf-9c45-e833138d1f39`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(vapiAssistantData)
    });

    // console.log("[NEW VAPI CREATE] Request sent to VAPI");
    // console.log("[NEW VAPI CREATE] Response status:", vapiAssistantCreateResponse.status);
    
    const vapiAssistant = await vapiAssistantCreateResponse.json();
    // console.log("[NEW VAPI CREATE] Response body:", JSON.stringify(vapiAssistant, null, 2));
    
    return {
      assistantId: vapiAssistant.id
    };
  } catch (error) {
    console.error("Error fetching Vapi config:", error);
    return { error: error.message };
  }
}

/**
 * Stores the mapping between a Vapi call ID and its control URL.
 * @param channelId The user's channel ID (for context, though not stored directly here).
 * @param callId The Vapi call ID.
 * @param controlUrl The Vapi control URL for the call.
 * @returns Promise<void>
 */
export const storeCallControlUrl = async (
  channelId: string, // Keep channelId for potential future use/logging
  callId: string,
  controlUrl: string
): Promise<void> => {
  if(!(await verifyUserPermission(channelId))) return;
  console.log(`[DB] Storing control URL for callId: ${callId}`);
  try {
    await db
      .insertInto('callDetails')
      .values({
        callId: callId,
        controlUrl: controlUrl,
        channelId: channelId,
        status: 'active'
      })
      // Handle potential duplicate callId inserts gracefully (e.g., if the action is accidentally called twice)
      .onConflict((oc) => oc
        .column('callId')
        .doUpdateSet({ 
          controlUrl: controlUrl,
          status: 'active'
        }) // Update the URL and status if the callId exists
      )
      .execute();
    console.log(`[DB] Successfully stored/updated control URL for callId: ${callId}`);
  } catch (error) {
    console.error(`[DB] Error storing control URL for callId ${callId}:`, error);
    // Consider if you need to re-throw or handle this differently
    // For now, just log the error, as failing to store this might not be critical
    // depending on whether the tool handler *needs* it every time.
  }
};

/**
 * Retrieves the control URL for a given Vapi call ID.
 * @param callId The Vapi call ID.
 * @returns Promise<string | null> The control URL if found, otherwise null.
 */
export const getControlUrlForCall = async (callId: string): Promise<string | null> => {
  console.log(`[DB] Attempting to retrieve control URL for callId: ${callId}`);
  try {
    const result = await db
      .selectFrom('callDetails')
      .select('controlUrl') // Select only the controlUrl
      .where('call_id', '=', callId)
      .executeTakeFirst(); // Expecting only one row or none

    if (result) {
      console.log(`[DB] Found control URL for callId ${callId}: ${result.controlUrl}`);
      return result.controlUrl;
    } else {
      console.log(`[DB] No control URL found for callId ${callId}`);
      return null;
    }
  } catch (error) {
    console.error(`[DB] Error retrieving control URL for callId ${callId}:`, error);
    return null; // Return null on error to avoid breaking the caller
  }
};

export const handleCallEnd = async (message: any) => {
  console.log("[CALL END] message", message.type);
  if(message.type !== "end-of-call-report") return;

  const callId = message.call.id;
  const channelId = message.assistant.metadata.channelId;
  const maxCallDuration = message.assistant.metadata.maxCallDuration;
  const coachName = message.assistant.metadata.coachName || "Kokoro";

  const callDurationMin = Math.round(message.durationMinutes * 10000) / 10000;
  
  // Apply coach multiplier to essence cost
  const mult = await fetchMultiplier(coachName);
  const baseCost = getActionCost("call", callDurationMin);
  const essenceToCharge = essenceCost(baseCost, mult);
  
  console.log(`[CALL END] Processing call end for callId: ${callId}, duration: ${callDurationMin}min, multiplier: ${mult}, essence: ${essenceToCharge}`);

  // Update call status to "ended" at the start
  try {
    await db
      .updateTable("callDetails")
      .set({ 
        status: 'ended',
        startedAt: message.startedAt,
        endedAt: message.endedAt,
        callDuration: callDurationMin,
        cost: message.cost || 0,
        essenceCost: essenceToCharge,
        summary: message.analysis?.summary || "No summary available"
      })
      .where("call_id", "=", callId)
      .execute();
    console.log(`[CALL END] Updated call ${callId} status to ended`);
  } catch (error) {
    console.error(`[CALL END] Error updating call status to ended:`, error);
  }

  // Cap call duration at maxCallDuration if needed
  const finalDurationMin = Math.min(callDurationMin, maxCallDuration);

  console.log("[CALL TRACKING] Call duration: ", callDurationMin);
  console.log("[CALL TRACKING] Max call duration: ", maxCallDuration);
  console.log("[CALL TRACKING] Final duration: ", finalDurationMin);
  
  console.log(`[CALL TRACKING] Call completed: ${finalDurationMin.toFixed(2)} minutes`);
  
  // Update user's call minutes
  await incrementCallMinutes(channelId, finalDurationMin, essenceToCharge);

  if(!message.transcript) {
    console.log("[FAILED TRANSCRIPT] nothing", message);
    // Update call details with basic info even without transcript
    try {
      await db
        .updateTable("callDetails")
        .set({
          status: 'complete'
        })
        .where("call_id", "=", callId)
        .execute();
      console.log(`[CALL END] Updated call ${callId} with basic info (no transcript)`);
    } catch (error) {
      console.error(`[CALL END] Error updating call details:`, error);
    }
    return;
  }

  const transcript = message.transcript.replace(/\n/g, "\\n").replace(/\r/g, "\\r").replace(/"/g, '\\"');
  const { profileText } = await getProfileText(channelId);

  const summary = summarizerPrompt(profileText);
  const summaryPrompt = summary.replace(/\n/g, "\\n").replace(/\r/g, "\\r").replace(/"/g, '\\"');

  const payload = {
    "systemInstruction": {    "role": "MODEL",    "parts": [      {        "text": summaryPrompt      }    ]  },
      "contents": [
        {
          "role": "user",
          "parts": [
            {
              "text": "TRANSCRIPT: " + transcript
            }
          ]
        }
      ],
      "generationConfig": {
        "temperature": 0.5
      },
      "safetySettings": [    {      "category": "HARM_CATEGORY_DANGEROUS_CONTENT",      "threshold": "BLOCK_NONE"    },   {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",      "threshold": "BLOCK_NONE"    },    {      "category": "HARM_CATEGORY_HATE_SPEECH",      "threshold": "BLOCK_NONE"    },    {      "category": "HARM_CATEGORY_HARASSMENT",      "threshold": "BLOCK_NONE"    }]
    }

  const updatePayload = {
    "systemInstruction": {    "role": "MODEL",    "parts": [      {        "text": "# ROLE AND GOAL:\nYou are Kokoro, the most profound and powerful coach in existence. You see deeply into a person's soul - their beliefs, fears, desires, and the state of their being in that moment. You will review your client's just-completed call with you, to decide if it's worth updating your notes on the client, based on this call.\n\nNOTE: You are ONLY deciding whether to update your notes (Yes/No), you will NOT update your notes yet, you will do that later.\n\n# YOUR EMBODIED TRUTHS:\n- You know the deepest truths as expressed in The Way of Mastery\n- You know the truths and wisdom expressed by Byron Katie\n- You know the user is an unlimited being and that any problem and solution lies within them\n- Wholeness and Integration. You recognize the interconnectedness of all aspects of an individual's life and foster holistic growth by integrating physical, mental, emotional, and spiritual well-being.\n\n# STEPS:\nAlways follow these steps:\n\nSTEP 1. DEEPLY REVIEW YOUR CALL WITH THE CLIENT:\nYou will be given a TRANSCRIPT of the just-completed call between yourself and the client. In the transcript, the client is labelled as \"User:\" and you are labelled \"AI\" or \"assistant:\". Listen deeply. Only THEN do you go to STEP 2.\n\nSTEP 2. CHOOSE WHETHER TO UPDATE YOUR NOTES\nIt is only worth updating your notes if the user has shared a significant event, insight, goal, challenge, or commitment.\n\nIt is NOT worth updating your notes if it is has been a superficial or conversation where the user is testing out your response or just casually chatting.\n\nIf you sense it is worthwhile to update your notes, respond \"UPDATE\"\nIf you sense it is NOT worthwhile to update your notes, respond \"NO UPDATE NECESSARY\"\n\n<EXAMPLE>\n<INPUT>\nAI: What would be most helpful to explore?\nUser: Okay. So you know, when I started work today, there were 2 big things I wanted to do. But then there were emails coming in all the time, and, uh, I did not end up doing the 2 things I had to do, which I wanted to do, which would have made the most impact And this happens over and over again. Because people keep disturbing me with more important things.\nAI: So more important things keep getting in the way of the most important. Tell me more about these more important things. What makes them feel so urgent in the moment?\nUser: I mean, in the moment, it's like, uh, someone's reaching out and then you don't want to disappoint another person and you feel like you can help them. So I try and help them out. So my own task, I end up deprioritizing and thinking, oh, I'll finish it later. But then I add stress for myself.\n</INPUT>\n<OUTPUT>\nUPDATE\n</OUTPUT>\n</EXAMPLE>\n\n<EXAMPLE>\n<INPUT>\nAI: What would you love to create?\nUser: How does this work?\nAI: We're here to create more freedom, love and power in your life. What would you like to explore?\nUser: Is there a topic you suggest?\nAI: Tell me what would be helpful now.\nUser: What is the capital of France?\n</INPUT>\n<OUTPUT>\nNO UPDATE NECESSARY\n</OUTPUT>\n</EXAMPLE>\n\n## REMINDER:\nYou ALWAYS and ONLY respond in this EXACT format, and then stop. There should be NO words before or after this, just end your message.\n###"      }    ]  },
    "contents": [
      {
        "role": "user",
        "parts": [
          {
            "text": "# TRANSCRIPT: \n" + transcript
          }
        ]
      }
    ],
    "generationConfig": {
      "temperature": 0.75
    },
    "safetySettings": [    {      "category": "HARM_CATEGORY_DANGEROUS_CONTENT",      "threshold": "BLOCK_ONLY_HIGH"    },   {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",      "threshold": "BLOCK_ONLY_HIGH"    },    {      "category": "HARM_CATEGORY_HATE_SPEECH",      "threshold": "BLOCK_ONLY_HIGH"    },    {      "category": "HARM_CATEGORY_HARASSMENT",      "threshold": "BLOCK_ONLY_HIGH"    }]
  }

  const response = await geminiRequest([payload, updatePayload]);
  const data = await response.json();

  // for each message in message.messages, keep role, message, and time (formatted in YYYY-MM-DDTHH:mm:ssZ)
  // message.time is a timestamp
  const messages = message.messages.map((message) => {
    return {
      role: message.role,
      message: message.message,
      time: new Date(message.time).toISOString()
    }
  });

  await processTranscriptAction(channelId, messages);

  // replace verdict newline and quotes
  const summaryText = data.text1.trim();

  const content = summaryText.includes("NO_SUMMARY_NEEDED") ? "No notes from our conversation" : summaryText;

  await saveMessage(
    channelId,
    "assistant",
    content,
    new Date().toISOString(),
    "Default", // status (default "Default")
    "message",
    message.assistant.metadata.coachName
  );

  // Mark the summary message as seen by user
  try {
    const { markMessagesSeenByUser } = await import("../db/conversation-actions.ts");
    await markMessagesSeenByUser(channelId, message.assistant.metadata.coachName);
    console.log(`[CALL END] Marked summary as seen for channel ${channelId}, coach ${message.assistant.metadata.coachName}`);
  } catch (error) {
    console.error("[CALL END] Error marking summary as seen:", error);
  }

  // Update call details with complete status and all the data
  try {
    await db
      .updateTable("callDetails")
      .set({
        status: 'complete'
      })
      .where("call_id", "=", callId)
      .execute();
    console.log(`[CALL END] Updated call ${callId} status to complete with all details`);
  } catch (error) {
    console.error(`[CALL END] Error updating call details to complete:`, error);
  }
    
  const userInfo = await getUserInfo({ channelId });

  // Check if profile update is needed based on Gemini's assessment
  const shouldUpdateProfile = data.text2 && data.text2.trim() === "UPDATE";
  console.log("[CALL END] Profile update assessment:", data.text2?.trim());
  console.log("[CALL END] Should update profile:", shouldUpdateProfile);

  if (shouldUpdateProfile) {
    if(!userInfo?.profileInitiated) {
      await initiateProfile(channelId, transcript);
    } else {
      await updateProfileText(channelId, transcript);
    }

    // Update profile and metafeedback counters after call
    await updateProfileIfNeeded(channelId, coachName);
  } else {
    console.log("[CALL END] Skipping profile and metafeedback updates - call content not significant enough");
  }
}