import { Kysely } from "npm:kysely";

export async function up(db: <PERSON>ysely<unknown>): Promise<void> {
  // Add cancellation-related fields to subscription table
  try {
    await db.schema
      .alterTable("subscription")
      .addColumn("cancel_at_period_end", "boolean", (col) => col.defaultTo(false))
      .execute();
    console.log("Added cancel_at_period_end column to subscription table");
  } catch (error) {
    if (!String(error).includes("already exists")) {
      throw error;
    } else {
      console.log("cancel_at_period_end column already exists, skipping");
    }
  }

  try {
    await db.schema
      .alterTable("subscription")
      .addColumn("canceled_at", "timestamp")
      .execute();
    console.log("Added canceled_at column to subscription table");
  } catch (error) {
    if (!String(error).includes("already exists")) {
      throw error;
    } else {
      console.log("canceled_at column already exists, skipping");
    }
  }

  console.log("Migration 50-add-subscription-cancellation-fields completed");
}

export async function down(db: <PERSON>ysely<any>): Promise<void> {
  // Remove cancellation fields from subscription table
  const columns = [
    "cancel_at_period_end",
    "canceled_at"
  ];

  for (const column of columns) {
    try {
      await db.schema
        .alterTable("subscription")
        .dropColumn(column)
        .execute();
      console.log(`Dropped ${column} column from subscription table`);
    } catch (error) {
      console.log(`Failed to drop ${column} column:`, error);
    }
  }
}
