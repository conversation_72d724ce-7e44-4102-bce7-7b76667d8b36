import React, { useState } from "npm:react@canary";
import { Button, Text } from "@reframe/ui/main.tsx";

export const TestNotifications = () => {
  const [channelId, setChannelId] = useState("");
  const [title, setTitle] = useState("Test Notification");
  const [message, setMessage] = useState("This is a test notification from Awaken");
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const sendTestNotification = async () => {
    if (!channelId.trim()) {
      alert("Please enter a channel ID");
      return;
    }

    setLoading(true);
    setResult(null);

    try {
      const response = await fetch('/api/push-notifications/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          channelId: channelId.trim(),
          title: title.trim(),
          message: message.trim(),
        }),
      });

      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ 
      padding: "20px", 
      maxWidth: "600px", 
      margin: "0 auto",
      fontFamily: "Arial, sans-serif",
      backgroundColor: "#000",
      color: "#fff",
      minHeight: "100vh"
    }}>
      <h1 style={{ color: "#FCA311", marginBottom: "30px" }}>
        Push Notification Test
      </h1>
      
      <div style={{ marginBottom: "20px" }}>
        <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>
          Channel ID:
        </label>
        <input
          type="text"
          value={channelId}
          onChange={(e) => setChannelId(e.target.value)}
          placeholder="Enter user channel ID"
          style={{
            width: "100%",
            padding: "10px",
            backgroundColor: "#333",
            color: "#fff",
            border: "1px solid #555",
            borderRadius: "4px",
            fontSize: "16px"
          }}
        />
      </div>

      <div style={{ marginBottom: "20px" }}>
        <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>
          Title:
        </label>
        <input
          type="text"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          style={{
            width: "100%",
            padding: "10px",
            backgroundColor: "#333",
            color: "#fff",
            border: "1px solid #555",
            borderRadius: "4px",
            fontSize: "16px"
          }}
        />
      </div>

      <div style={{ marginBottom: "20px" }}>
        <label style={{ display: "block", marginBottom: "5px", fontWeight: "bold" }}>
          Message:
        </label>
        <textarea
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          rows={3}
          style={{
            width: "100%",
            padding: "10px",
            backgroundColor: "#333",
            color: "#fff",
            border: "1px solid #555",
            borderRadius: "4px",
            fontSize: "16px",
            resize: "vertical"
          }}
        />
      </div>

      <button
        onClick={sendTestNotification}
        disabled={loading}
        style={{
          backgroundColor: "#FCA311",
          color: "#000",
          border: "none",
          padding: "12px 24px",
          borderRadius: "4px",
          fontSize: "16px",
          fontWeight: "bold",
          cursor: loading ? "not-allowed" : "pointer",
          opacity: loading ? 0.6 : 1,
          marginBottom: "20px"
        }}
      >
        {loading ? "Sending..." : "Send Test Notification"}
      </button>

      {result && (
        <div style={{
          backgroundColor: "#333",
          padding: "15px",
          borderRadius: "4px",
          border: "1px solid #555"
        }}>
          <h3 style={{ color: "#FCA311", marginTop: 0 }}>Result:</h3>
          <pre style={{
            backgroundColor: "#222",
            padding: "10px",
            borderRadius: "4px",
            overflow: "auto",
            fontSize: "14px",
            color: "#fff"
          }}>
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}

      <div style={{
        marginTop: "30px",
        padding: "15px",
        backgroundColor: "#1a1a1a",
        borderRadius: "4px",
        border: "1px solid #333"
      }}>
        <h3 style={{ color: "#FCA311", marginTop: 0 }}>Instructions:</h3>
        <ol style={{ lineHeight: "1.6" }}>
          <li>Make sure you have the Expo app installed and running</li>
          <li>Log in to the app so your push token is registered</li>
          <li>Find your channel ID (it's displayed in the web app when logged in)</li>
          <li>Enter your channel ID above and click "Send Test Notification"</li>
          <li>You should receive a push notification on your mobile device</li>
        </ol>
      </div>
    </div>
  );
};
