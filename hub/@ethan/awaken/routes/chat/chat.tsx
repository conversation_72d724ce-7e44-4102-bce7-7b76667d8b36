"use client";

import { useEffect, useRef, useState } from "npm:react@canary";
import React from "npm:react@canary";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  ScrollArea,
  Text,
} from "@reframe/ui/main.tsx";

import {
  useSpring,
  animated,
  config,
  useTransition,
} from "npm:@react-spring/web";

import { FireSvg } from "./components/FireSvg.tsx";
import { ChatMessage } from "./chat-message/chat-message.tsx";
import {
  Messages,
} from "../../lib/constants.ts";
import { checkCallLimit, getFirstMessage, getReadableStream, getSummaryInfo, getUsage, makeSummaryFalse, setFirstMessageFalse, updateTimeZone } from "../../action.ts";
import { appendProfileAction } from "../../actions/db/profile-submit.ts";
import { checkIsSuperUser } from "../../lib/auth-helper.ts";
import { createMessageResponse } from "../../actions/ai/prompt-actions.ts";
import { SquareMessageIcon } from "../../lib/icons.tsx";
import { AudioLines } from "@reframe/icons/audio-lines.ts";
import { MenuIcon } from "@reframe/icons/icons/MenuIcon.ts";

import { Logo } from "../../lib/logo.tsx";
import { WelcomeMessageComponent } from "./chat-message/components/WelcomeMessage.tsx";
import { NoticeOverlay } from "./components/NoticeOverlay.tsx"
import { Layout } from "../../lib/layout.tsx";
import { getLatestNotice, updateLastSeenNotice } from "../../action.ts";
import { SubscriptionSuccess } from "../../lib/profile/subscription-success.tsx";
import { PLANS, PlanType } from "../../lib/plans.ts";
import { 
  getAllMessages, 
} from "../../action.ts";
// Add import for generateEnrolmentMessage if not already present
import { generateEnrolmentMessage, generateAudio } from "../../action.ts";
import { updateMessageStatus, uploadAudio, markMessagesSeenByUser } from "../../lib/conversation-db.ts";
import { fetchAndSyncSelectedCoach, setSelectedCoach } from "./components/coach-utils.ts";
import { getUserData } from "../../lib/db.ts";
import { UserData } from "../../lib/db.ts";
// Add AnimatePresence import
// Add this to the imports at the top
import { TOP_UP_OPTIONS, TopUpOverlay } from "../../lib/top-up-overlay.tsx";
// Add import for the new server action
// Import the new component
import { SwipeableCards } from "./components/SwipeableCards.tsx";
import { FirstMessageComponent } from "./chat-message/components/FirstMessageComponent.tsx";
import { getCoachAction, getUserCoachesAction } from "../../actions/db/coach-actions.ts"; // <-- Import getUserCoachesAction and fix previous import
// Import the CoachOverlay component
import { CoachOverlay } from "./components/CoachOverlay.tsx";
import { CoachSelector } from "./components/CoachSelector.tsx";
import { CoachList } from "./components/CoachList.tsx";
// --- Add Imports for Card Actions ---
// Import the new action
import { generateGuidedSessionPlanAction } from '../../actions/ai/session-plan-actions.ts';

import { Circle } from "./components/Circle.tsx";
import { useAudioPlayer } from "../../lib/hooks/useAudioPlayer.tsx";
import { PaymentOverlay } from "../../lib/profile/payment-overlay.tsx";
import { VoiceCall } from "./components/VoiceCall.tsx";
import { UserCardView } from "../../actions/db/coach-actions.ts";

// Array of possible bullet points for the placeholder
const PLACEHOLDER_BULLET_POINTS = [
  "get help with... ",
  "solve... ", 
  "process... ",
  "find... ",
  "explore... ",
  "overcome... ",
  "develop... ",
  "improve... ",
  "reduce... ",
  "increase... ",
  "navigate... ",
  "be... ",
  "grow... ",
  "share... ",
  "change... ",
  "practice... ",
  "discover... ",
  "build... ",
  "let go of... ",
  "think through... ",
  "cultivate... ",
  "enhance... ",
  "connect with... ",
  "create... ",
  "decide... ",
  "prepare... ",
  "talk... ",
  "feel... ",
];

// Function to get random bullet points
const getRandomBulletPoints = (count = 3) => {
  const shuffled = [...PLACEHOLDER_BULLET_POINTS].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count).map(point => `${point}`).join('\n');
};

// Array of prompt texts
const PROMPT_TEXTS = [
  "What does your heart desire?",
  "What do you want to explore?",
  "What's on your mind?",
  "What are you curious about?",
  "What do you want to discover?",
  "What guidance do you seek?",
  "What's present for you?",
  "What's alive for you?",
  "What brings you here today?",
  "What would make a difference?",
  "What would you love?",
  "What would serve you most?",
  "What's calling you?",
  "What's your intention?",
  "What's asking to be seen?",
  "What's asking for attention?",
  "What's present for you?",
];

// Function to get a random prompt text
const getRandomPromptText = () => {
  const randomIndex = Math.floor(Math.random() * PROMPT_TEXTS.length);
  return PROMPT_TEXTS[randomIndex];
};

// Function to convert coach name to portrait image URL
const getCoachPortraitUrl = (coachName: string): string => {
  const fileName = coachName.toLowerCase().replace(/\s+/g, '-') + '-portrait.png';
  return `https://storage.googleapis.com/awaken-audio-files/${fileName}`;
};

// Function to detect if the user is on a mobile device
const isMobileDevice = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    typeof navigator !== 'undefined' ? navigator.userAgent : ''
  );
};

const rethinkSansFontUrl =
  "https://fonts.googleapis.com/css2?family=Rethink+Sans:ital,wght@0,400..800;1,400..800&display=swap";

const rethinkSansFontStyles = `
  @import url('${rethinkSansFontUrl}');

  /* Use the font-family */
  .component-a {
    font-family: "Rethink+Sans", sans-serif;
  }

  /* Add animation for button glow */
  @keyframes pulse-glow {
    0% {
      box-shadow: 0 0 0 0 rgba(252, 163, 17, 0.7), 0 0 0 0 rgba(255, 255, 255, 0.2); /* Add a subtle white inner glow */
    }
    70% {
      box-shadow: 0 0 0 12px rgba(252, 163, 17, 0), 0 0 0 4px rgba(255, 255, 255, 0); /* Make the orange glow larger, white inner glow expands less */
    }
    100% {
      box-shadow: 0 0 0 0 rgba(252, 163, 17, 0), 0 0 0 0 rgba(255, 255, 255, 0); /* Fade back to nothing */
    }
  }

  .animate-pulse-glow {
    animation: pulse-glow 1.8s infinite ease-out; /* Slightly faster and smoother easing */
    /* Background removed, relying solely on box-shadow for the glow */
  }
`;



// Find the component props definition - likely at the beginning
// and update the enrollmentMessage type to be optional
interface ChatProps {
  user: any;
  messages: any[];
  enrollmentMessage: string | null; // Update to allow null
  initialUserData: UserData;
  availableCoaches: any[];
}

export const Chat = ({ user, messages, enrollmentMessage: initialEnrollmentMessage, initialUserData, availableCoaches }: ChatProps) => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  const [isPaymentOpen, setIsPaymentOpen] = useState(false);
  
  // Add state to track timezone sync status
  const [timeZoneSynced, setTimeZoneSynced] = useState(false);
  
  // Add ref to track touch start X position for swipe gesture
  const startXRef = useRef<number | null>(null);
  
  // Add state for coach selection - use lazy initialization to check localStorage first
  // Initialize coachName to empty string to avoid hydration mismatch
  const [coachName, setCoachName] = useState<string>('');

  // Add new state for chat visibility
  const [chatVisible, setChatVisible] = useState(false);

  // Add state to track navigation direction
  const [navigationDirection, setNavigationDirection] = useState<'forward' | 'backward'>('forward');


  // isComplete state is now managed by the AudioPlayer component
  const [setIsComplete] = useState<(value: boolean) => void>(() => (val: boolean) => {});

  // Add state for dynamic message loading
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [oldestMessageDate, setOldestMessageDate] = useState<string | null>(null);
  const [page, setPage] = useState(1);

  const [isTopUpOpen, setIsTopUpOpen] = useState(false);
  
  // Voice call related states 
  const [isVoiceCall, setIsVoiceCall] = useState(false);
  const [voiceCallCard, setVoiceCallCard] = useState<UserCardView | null>(null);

  const toggleVoiceCall = (card: UserCardView | null = null) => {
    if (isVoiceCall) {
      setIsVoiceCall(false);
      return;
    }
    
    // Transform UserCardView to CardContext structure if card exists
    if (card) {
      const cardContext: any = {
        userCardId: card.user_card_id,
        coachOfferingId: card.coach_offering_id,
        type: card.type,
        data: card.instance_data,
        uiConfig: card.uiConfig,
        goalInput: card.goalInput
      };
      setVoiceCallCard(cardContext);
    } else {
      setVoiceCallCard(null);
    }
    
    setIsVoiceCall(true);
  };

  // Hide drawer when in fullscreen chat, or when a specific coach conversation is active
  // Detect and sync user timezone and coach selection
  useEffect(() => {
    if (!user?.channelId) return;

    // availableCoaches.forEach((name: string) => {
    //   const img = new Image();
    //   img.src = getCoachPortraitUrl(name);
    // });
    
    // Fetch selected coach from database and sync with localStorage
    // This will call setCoachName when the data is ready
    fetchAndSyncSelectedCoach(user.channelId, (syncedCoach) => {
      // Use a function update for setCoachName to access the latest availableCoaches
      setCoachName(currentCoachName => {
        // Read the *latest* availableCoaches state inside the setter
        const currentAvailableCoaches = availableCoachesRef.current;
        if (syncedCoach && currentAvailableCoaches.includes(syncedCoach)) {
          return syncedCoach;
        } else if (currentAvailableCoaches.length > 0) {
          const defaultCoach = currentAvailableCoaches[0];
          // Also update the storage/DB with this default
          setSelectedCoach(user.channelId, defaultCoach);
          return defaultCoach;
        }
        // Fallback if no coaches available and no synced coach
        return currentCoachName; // Keep existing or null
      });
    });
    
    if (timeZoneSynced) return;
    
    const tz = Intl.DateTimeFormat().resolvedOptions().timeZone;
    // e.g. "America/New_York" or "Asia/Kolkata"

    // Only do this once per session:
    updateTimeZone(user.channelId, tz)
      .then((data) => {
        if (data.success) {
          console.log("[TIMEZONE] Synced user time zone:", tz);
          setTimeZoneSynced(true);
        }
      })
      .catch((err) => console.error("[TIMEZONE] Error:", err));
  }, [user?.channelId]); // ONLY depend on user?.channelId

  // Use a ref to keep track of the latest availableCoaches without causing effect re-runs
  const availableCoachesRef = useRef(availableCoaches);
  useEffect(() => {
    availableCoachesRef.current = availableCoaches;
  }, [availableCoaches]);

  // Constants for message loading
  const STARTING_MESSAGE_COUNT = 25;
  const SHOW_MORE_MESSAGE_COUNT = 25;
  const MAX_MESSAGES = 400; // Maximum number of messages to load

  // Audio element reference for backward compatibility
  const rootAudioRef = useRef<HTMLAudioElement>(null);
  
  // Add ref for goal input textarea
  const goalInputRef = useRef<HTMLTextAreaElement>(null);

  const [channelId, setChannelId] = useState(null);

  const [showWelcome, setShowWelcome] = useState(false);
  const [showFirstMessage, setShowFirstMessage] = useState(false);

  const [input, setInput] = useState("");
  const [goalInput, setGoalInput] = useState("");
  const [output, setOutput] = useState("");
  const [originalOutput, setOriginalOutput] = useState("");
  const [outputArray, setOutputArray] = useState([]);
  const [placeholderText, setPlaceholderText] = useState(""); // Add state for placeholder
  const [placeholderOpacity, setPlaceholderOpacity] = useState(1); // Add state for opacity
  const [isTransitioning, setIsTransitioning] = useState(false); // Track transition state
  const placeholderIntervalRef = useRef<number | null>(null); // Reference for interval

  const [loading, setLoading] = useState(false);

  const [allMessages, setAllMessages] = useState<Messages>([]);

  // const [audio, setAudio] = useState(null);

  const [messageLoaded, setMessageLoaded] = useState(false);
  // Audio playback state is now managed by the AudioPlayer component
  const [setIsPlaying] = useState<(value: boolean) => void>(() => (val: boolean) => {});

  const [resetTimeout, setResetTimeout] = useState<Number | null>(null);

  const [firstMessage, setFirstMessage] = useState(false);

  const [isBegining, setIsBegining] = useState(false);

  const [onboarded, setOnboarded] = useState(true);

  const audioContextRef = useRef(null);
  const animationRef = useRef(null);
  const sourceRef = useRef(null);


  const [userData, setUserData] = useState(initialUserData);

  const [latestNotice, setLatestNotice] = useState(null);

  // Add state for message button glow animation
  const [isMessageButtonGlowing, setIsMessageButtonGlowing] = useState(false);

  const [stablePrompt, setStablePrompt] = useState("");

  // Add state for message usage tracking
  const [messageUsage, setMessageUsage] = useState({ count: 0, limit: 0 });

  // Add state for the enrollment message
  const [enrollmentMessage, setEnrollmentMessage] = useState<string | null>(initialEnrollmentMessage);
  const [isLoadingEnrollment, setIsLoadingEnrollment] = useState<boolean>(!initialEnrollmentMessage);

  // Add state for coach speaking status


  const [showSubscriptionSuccess, setShowSubscriptionSuccess] = useState(false);
  const [successPlanType, setSuccessPlanType] = useState('basic');
  const [successPlanTitle, setSuccessPlanTitle] = useState('Beyond');

  const [isInitialRender, setIsInitialRender] = useState(true);

  // Add state for displayed cards and loading
  // Card-related state has been moved to ChatMessage component

  // Add state for superuser status
  const [isSuperUser, setIsSuperUser] = useState(false); // <-- Add this state
  
  // Add useEffect to check superuser status on mount/email change
  useEffect(() => {
    const checkSuperUserStatus = async () => {
      if (user?.email) {
        const superUserStatus = await checkIsSuperUser(user.email);
        setIsSuperUser(superUserStatus);
      }
    };
    checkSuperUserStatus();
  }, [user?.email]); // <-- Add this useEffect

  // Preload coach portrait images on mount
  // useEffect(() => {
  //   AVAILABLE_COACHES.forEach(name => {
  //     const img = new Image();
  //     img.src = getCoachPortraitUrl(name);
  //   });
  // }, []); // Empty dependency array ensures this runs only once on mount

  // Add transition for coach image fade
  const coachImageTransition = useTransition(coachName || '', {
    key: coachName || '', // Use the coach name itself as the key, with fallback for null
    from: { opacity: 0 },
    enter: { opacity: 1 },
    leave: { opacity: 0 },
    config: { duration: 150 }, // Snappy duration (200ms)
    exitBeforeEnter: true, // Ensures fade-out completes before fade-in
    immediate: isInitialRender, // Skip animation on initial load
  });

  // Create an instance of the AudioPlayer
  const {
    isPlaying,
    isComplete,
    audioScale,
    togglePlay,
    play,
    audio,
    setAudio,
    stopAudio,
    initializeAudioContext,
  } = useAudioPlayer({
    onPlayStateChange: (playing) => {
      setIsPlaying(playing);
    },
    onComplete: () => {
      setIsComplete(true);
    },
  });

  const reset = () => {
    console.log("Resetting...");
    resetOutputSentence();
    setInput("");
    setOutput("");
    setOriginalOutput("");
    setIsComplete(false);
    setIsPlaying(false);
    setAudio(null);
  };

  const resetOutputSentence = () => {
    console.log("Resetting output sentence...");

    setOutputArray([]);
    setCurrentSentenceIndex(0);
    setMessageRendering(false);
    // setCurrentLetterIndex(0);
  };



  // Toggle star status in Airtable
  const toggleStar = async (msgId: string, currentStatus: string) => {
    try {
      // Skip if no message ID or it's a temporary ID
      if (!msgId || msgId.startsWith('temp-')) {
        console.log('Cannot star message: Invalid or temporary ID');
        return;
      }

      const newStatus = currentStatus === "Starred" ? "Default" : "Starred";
      const baseUrl = globalThis.location.origin;

      // const response = await fetch(`${baseUrl}/togglestar`, {
      //   method: "POST",
      //   headers: { "Content-Type": "application/json" },
      //   body: JSON.stringify({ recordId: msgId, newStatus }),
      // });

      const data = await updateMessageStatus(msgId, newStatus);
      console.log("toggleStar response:", data);
      
      setAllMessages((prev) =>
        prev.map((m) =>
          m.Id === msgId
            ? {
                ...m,
                Status: newStatus,
              }
            : m
        )
      );
    } catch (err) {
      console.error("Error toggling star:", err);
    }
  };

  const sendMessage = async (
    input: string | Blob,
    useAudio: boolean = false,
    isVoiceNote: boolean = false
  ) => {
    reset();
    setLoading(true);
    if(isVoiceNote) {
      setIsMessageButtonGlowing(true);
    }
    console.log("Sending message: ", input);
    // console.log("FormData: ", formData);
    // Only initialize audio context if we're using audio
    // if (useAudio) {
    //   initializeAudioContext();
    // }

    // Add user's message to local chat with temporary ID
    const tempId = `temp-${Date.now()}`;
    console.log("typeof message: ", input instanceof Blob);
    
    const isAudio = input instanceof Blob;
    console.log(isAudio ? "Blob" : "string");
    let objectURL = null;

    const message = (isAudio ? await input.arrayBuffer() : input);
    if(isAudio) {
      objectURL = URL.createObjectURL(input);
    }
    setAllMessages((prevMessages) => [
      ...prevMessages,
      {
        Id: tempId,
        Content: (typeof message === "string") ? message : "",
        Date: new Date().toISOString(),
        Sender: "user",
        isTemporary: true, // Add flag to mark temporary messages
        CoachName: coachName || "Kokoro", // Add coach name for consistency
        Audio: objectURL
      },
    ]);

    const baseUrl = globalThis.location.origin;
    let data: { output: any | null; audio: string | null, status: number | null, type: string | null, msg: string | null, userMessageId: string | null };
    let kokoroResponse: string | null = null;
    let userMessageId: string | null = null;
    let assistantMessageId: string | null = null;
    let uploadedAudio: string | null = null;
    let messageText: string | null = null;

    if (typeof message === "string" && message.trim().startsWith("#profile#")) {
        try {
            await appendProfileAction(message);
            setAllMessages(prev=>[
               ...prev,
               { Id:"local-"+Date.now(), Content:"✅ Profile updated.",
                 Date:new Date().toISOString(), Sender:"assistant",
                 CoachName: coachName }
            ]);
        } catch(e){
            alert("Profile update failed: "+e);
        }
        return;          // stop normal message flow
    }

    const decoder = new TextDecoder();
    async function read(reader: ReadableStreamDefaultReader) {
      while(true) {
        const { value, done } = await reader.read();
        if(done) break;
        const data = JSON.parse(decoder.decode(value));

        if(data.transcript) {
          // set transcript
          setAllMessages((prevMessages) => {
            return prevMessages.map(msg => {
              if (msg.Id === tempId) {
                return {
                  ...msg,
                  Content: data.transcript
                };
              }
              return msg;
            })
          });
        }

        else {
          kokoroResponse = data.output;
          userMessageId = data.userMessageId; // Get user message ID from response
          assistantMessageId = data.assistantMessageId;
          uploadedAudio = data.uploadedAudio;
          messageText = data.messageText;
        }
      }
    }
    try {
      // --- End Fetch Coach Data ---
      {
        // Use the determined model type and name in the call
        console.log("Channel ID: ", channelId);
        console.log("Coach Name: ", coachName);

        const stream = await createMessageResponse(
          channelId, 
          message, 
          coachName, // Pass determined type
          isVoiceNote,          // Pass isVoiceNote
          useAudio
        );

        console.log("DATA FROM WEBHOOK", data);
        if(stream.blocked) {
          // blocked == daily_msg_limit_reached or monthly_msg_limit_reached
          if(stream.action === "checkout") {
            setIsPaymentOpen(true);
            setLoading(false);
          }
          else if(stream.action === "top_up_xyz") {
            setIsTopUpOpen(true);
            setLoading(false);
          }
          return;
        }

        // stream sends two types of data
        // controller.enqueue(encoder.encode(JSON.stringify({ messageText })));
        // controller.enqueue(encoder.encode(JSON.stringify({
        //   output: aiResponse,
        //   userMessageId,
        //   assistantMessageId,
        //   uploadedAudio,
        //   messageText
        // })));
        // it might send only one
        // or both
        // i want to parse the jsons now

        
        const reader = stream.getReader();
        
        await read(reader);
        // ("DATA FROM WEBHOOK", data);
      }
    } catch (error) {
      console.error("Error sending message:", error);
      setIsComplete(true);
      setLoading(false);
      return;
    }

    if(typeof message === "string") messageText = message;
    
    
    if(!kokoroResponse) {
      setIsComplete(true);
      setLoading(false);
      return;
    }
    
    setIsComplete(true);

    setLoading(false);
    setOutput(kokoroResponse);
    setOriginalOutput(kokoroResponse);

    // Update messages with real IDs
    setAllMessages((prevMessages) => {
      return prevMessages.map(msg => {
        // Replace temporary user message with real ID from /createMessageResponse response
        if (msg.Id === tempId && userMessageId) {
          return {
            ...msg,
            Id: userMessageId,
            isTemporary: false,
            Content: messageText
          };
        }
        return msg;
      }).concat([{
        Id: assistantMessageId,
        Content: kokoroResponse,
        Date: new Date().toISOString(),
        Sender: "assistant",
        Audio: uploadedAudio,
        New: true,
        isTemporary: false,
        CoachName: coachName || "Kokoro" // Use the coachName state variable
      }]);
    });

    setInput("");
  };





  const [currentSentenceIndex, setCurrentSentenceIndex] = useState(0);
  const [currentLetterIndex, setCurrentLetterIndex] = useState(0);
  const [messageRendering, setMessageRendering] = useState(false);

  // Function to load older messages
  const loadMoreMessages = async () => {
    if (loadingMore || !hasMore) return;
    
    // Check if we've reached the maximum message limit
    if (allMessages.length >= MAX_MESSAGES) {
      console.log(`Reached maximum message limit (${MAX_MESSAGES})`);
      setHasMore(false);
      return;
    }
    
    try {
      setLoadingMore(true);
      
      const filteredMessages = allMessages.filter(msg => msg.CoachName === coachName || coachName === "all");
      // Find the oldest message date to use as cursor
      const oldestMsg = filteredMessages.length > 0 ? filteredMessages[0] : null;
      const beforeDate = oldestMsg?.Date || null;
      
      // Calculate how many more messages we can load without exceeding the limit
      const remainingMessages = MAX_MESSAGES - filteredMessages.length;
      const limit = Math.min(SHOW_MORE_MESSAGE_COUNT, remainingMessages);
      
      // If we can't load any more messages, stop here
      if (limit <= 0) {
        setHasMore(false);
        setLoadingMore(false);
        return;
      }
      
      // REPLACED: Directly call server action instead of fetch API
      // Add "all" as the coachName to show messages from all coaches
      const olderMessages = await getAllMessages(channelId, limit, beforeDate, coachName, ["message", "summary", "coach_message", "proactive_message"]);
      
      // If we got fewer messages than requested, we've reached the end
      if (olderMessages.length < limit) {
        setHasMore(false);
      }
      
      // If we got messages, prepend them to our message list
      if (olderMessages.length > 0) {
        // Ensure they're sorted correctly (oldest first)
        // const sortedMessages = olderMessages.sort((a, b) => {
        //   const dateA = a.Date ? new Date(a.Date).getTime() : 0;
        //   const dateB = b.Date ? new Date(b.Date).getTime() : 0;
        //   return dateA - dateB;
        // });
        
        // // Update the message list
        // setAllMessages(prev => [...sortedMessages, ...prev]);

        setAllMessages(prev => {
          const merged = [...olderMessages, ...prev];
          const seen = new Set<string>();
          return merged
            .filter(m => !seen.has(m.Id) && seen.add(m.Id))
            .sort((a, b) => {
              const dateA = a.Date ? new Date(a.Date).getTime() : 0;
              const dateB = b.Date ? new Date(b.Date).getTime() : 0;
              return dateA - dateB;
            });
        })
        
        // Check if we've reached the maximum after adding new messages
        if (allMessages.length >= MAX_MESSAGES) {
          console.log(`Reached maximum message limit after loading (${MAX_MESSAGES})`);
          setHasMore(false);
        }
        
        // Update the oldest message date for next fetch
        if (allMessages[0]?.Date) {
          setOldestMessageDate(allMessages[0].Date);
        }
      } else {
        // No more messages
        setHasMore(false);
      }
      
    } catch (error) {
      console.error("Error loading more messages:", error);
    } finally {
      setLoadingMore(false);
    }
  };

  // Update fetchAllMessages to use "all" for coachName
  const fetchAllMessages = async (channelId) => {
    try {
      // REPLACED: Directly call server action instead of fetch API
      // Add "all" as the coachName to show messages from all coaches
      const data = await getAllMessages(channelId, Math.min(STARTING_MESSAGE_COUNT, MAX_MESSAGES), undefined, "all", ["message", "summary", "coach_message", "proactive_message"]);
      
      // Add debug logging to see coach names
      if (data && data.length > 0) {
        const coachNames = [...new Set(data.map(m => m.CoachName || 'unknown'))];
        // console.log(`[FRONTEND] Coach names in fetched messages: ${coachNames.join(', ')}`);
        
        // Log a sample message
        if (data[0]) {
          console.log('[FRONTEND] Sample message:', {
            id: data[0].Id,
            coachName: data[0].CoachName,
            sender: data[0].Sender,
            contentPreview: data[0].Content.slice(0, 30) + '...'
          });
        }
      }
      
      setAllMessages(data);

      // Set the oldest message date for pagination
      if (data.length > 0) {
        // Find the oldest message by date
        const sorted = [...data].sort((a, b) => {
          const dateA = a.Date ? new Date(a.Date).getTime() : 0;
          const dateB = b.Date ? new Date(b.Date).getTime() : 0;
          return dateA - dateB;
        });
        
        if (sorted[0]?.Date) {
          setOldestMessageDate(sorted[0].Date);
        }
        
        // If we got fewer messages than the limit, or we've reached the max, we've reached the end
        setHasMore(data.length >= STARTING_MESSAGE_COUNT && data.length < MAX_MESSAGES);
      } else {
        setHasMore(false);
      }

      // check the last message and it's date and if it is less than one hour, then set the call started to true
      const lastMessage = data[data.length - 1];
      if (lastMessage) {
        const lastMessageDate = new Date(lastMessage.Date ?? "");

        const currentDate = new Date();
        const diff = Math.abs(currentDate.getTime() - lastMessageDate.getTime());
        const diffMinutes = Math.floor(diff / 60000);

        if (diffMinutes < 60) {
          setOriginalOutput(lastMessage.Content);
          setAudio(lastMessage.Audio);
          setIsComplete(true);
          setIsBegining(true);
        }
      }
    } catch (error) {
      console.error("Error fetching all messages:", error);
      setAllMessages([]);
      setHasMore(false);
    }
  };

  // Add this state variable near other state variables
  const [isFirstMessageFetching, setIsFirstMessageFetching] = useState(false);
  const hasSetFirstMessageFalse = useRef(false);

  // Fetch the first message
  const fetchFirstMessage = async (channelId) => {
    // Prevent duplicate calls
    if (isFirstMessageFetching || hasSetFirstMessageFalse.current) {
      console.log("[FIRST_MESSAGE] Already fetching or already set to false, skipping duplicate call");
      return;
    }
    
    setIsFirstMessageFetching(true);
    
    try {
      console.log("[FIRST_MESSAGE] Fetching first message for channel", channelId);
      const data = await getFirstMessage(channelId);

      if (!data || !data.Content) {
        console.error("[FIRST_MESSAGE] Missing or invalid first message data", data);
        setLoading(false);
        setIsFirstMessageFetching(false);
        return;
      }

      console.log("[FIRST_MESSAGE] Successfully received first message");

      setOutput(data.Content);
      setOriginalOutput(data.Content);
      setLoading(false);

      if(data.Audio) {
        play(data.Audio);
      }

      setIsComplete(true);
      console.log("[FIRST_MESSAGE] setIsComplete(true)");

      // Audio will be initialized automatically by the AudioPlayer component
      // when audio state is set and autoPlay is true

      // Only update firstMessage flag in database once per session
      if (!hasSetFirstMessageFalse.current) {
        hasSetFirstMessageFalse.current = true;
        setFirstMessageFalse(channelId).then(() => {
          console.log("Set first message to false in database");
        });
      }

      // Fetch all messages after adding the first one
      fetchAllMessages(channelId);

      setFirstMessage(false);
    } catch (error) {
      console.error("[FIRST_MESSAGE] Error fetching first message:", error);
    } finally {
      setIsFirstMessageFetching(false);
    }
  };

  // Update placeholder with smooth transition
  const updatePlaceholderWithTransition = () => {
    // Don't run if already transitioning
    if (!isTransitioning) {
      // Always update the placeholder text in the background
      const newPlaceholderText = getRandomBulletPoints();
      
      // Only show visual transition if input is empty
      if (!goalInput.trim()) {
        setIsTransitioning(true);
        // Fade out
        setPlaceholderOpacity(0);
        
        // After fade out completes, update text and fade in
        setTimeout(() => {
          setPlaceholderText(newPlaceholderText);
          setPlaceholderOpacity(1);
          
          // Reset transitioning flag after animation completes
          setTimeout(() => {
            setIsTransitioning(false);
          }, 800);
        }, 600); // Match this with the CSS transition duration
      } else {
        // If there's input, just update the text without animation
        setPlaceholderText(newPlaceholderText);
      }
    }
  };

  // Runs when the component mounts
  useEffect(() => {

    setChannelId(user.channelId);
    setOnboarded(!user.firstMessage);
    
    // Set initial stable prompt text once
    setStablePrompt(getRandomPromptText());
    
    // Set initial placeholder text
    setPlaceholderText(getRandomBulletPoints());
    
    // Set up interval to update placeholder text every 15 seconds
    placeholderIntervalRef.current = globalThis.setInterval(() => {
      updatePlaceholderWithTransition();
    }, 15000); // 15 seconds

    // Use server-side rendered messages if provided
    if (messages && messages.length > 0) {
      console.log("Using server-side rendered messages:", messages.length);
      setAllMessages(messages);
      
      // Set pagination data
      if (messages.length > 0) {
        // Find the oldest message by date for pagination
        const sorted = [...messages].sort((a, b) => {
          const dateA = a.Date ? new Date(a.Date).getTime() : 0;
          const dateB = b.Date ? new Date(b.Date).getTime() : 0;
          return dateA - dateB;
        });
        
        if (sorted[0]?.Date) {
          setOldestMessageDate(sorted[0].Date);
        }
        
        // If we got fewer messages than the limit, or we've reached the max, we've reached the end
        setHasMore(messages.length >= STARTING_MESSAGE_COUNT && messages.length < MAX_MESSAGES);
        
        // Process last message if recent
        const lastMessage = messages[messages.length - 1];
        if (lastMessage) {
          const lastMessageDate = new Date(lastMessage.Date ?? "");
          const currentDate = new Date();
          const diff = Math.abs(currentDate.getTime() - lastMessageDate.getTime());
          const diffMinutes = Math.floor(diff / 60000);

          if (diffMinutes < 60) {
            setOriginalOutput(lastMessage.Content);
            setAudio(lastMessage.Audio);
            setIsComplete(true);
            setIsBegining(true);
          }
        }
      } else {
        setHasMore(false);
      }
    }
    else if(!user.firstMessage) {
      fetchAllMessages(user.channelId);
    }
    
    if (user.firstMessage) {
      setShowWelcome(true);
      setFirstMessage(true);
      setLoading(true);
      // Don't fetch first message yet, wait for user to click the button
      // fetchFirstMessage(user.channelId);
    } 
    // Fall back to client-side fetching if no server-rendered messages and not first message
    
    setMessageLoaded(true);

    const style = document.createElement("style");
    style.textContent = rethinkSansFontStyles;
    document.head.appendChild(style);
    
    // Clean up interval on unmount
    return () => {
      document.head.removeChild(style);
      if (placeholderIntervalRef.current) {
        clearInterval(placeholderIntervalRef.current);
      }
    };
  }, []);

  const fileReader = useRef(null);

  // Split output into sentences when output changes
  useEffect(() => {
    if (output) {
      // Clear any existing timeout when new output arrives
      if (resetTimeout) {
        clearTimeout(resetTimeout);
      }

      // Split sentences as before
      const sentences = output
        .split(/(?<=[.!?])\s*/)
        .map((sentence) => sentence.trim())
        .filter((sentence) => sentence);
      setOutputArray(sentences);
      setCurrentSentenceIndex(0);
      setCurrentLetterIndex(0);
      setMessageRendering(true);

      // Set new 5-minute timeout
      const timeout = setTimeout(() => {
        reset();
      }, 60 * 60 * 1000); // 60 minutes in milliseconds

      setResetTimeout(timeout);

      // Cleanup on unmount or when output changes
      return () => {
        if (resetTimeout) {
          clearTimeout(resetTimeout);
        }
      };
    }
  }, [output]);

  useEffect(() => {
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (sourceRef.current) {
        sourceRef.current.disconnect();
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
      if (fileReader.current) {
        fileReader.current.abort();
      }
    };
  }, []);

  // Render the next letter every 100ms
  useEffect(() => {
    if (!output) return;

    if (currentLetterIndex >= output.length) {
      // Make the last message of the messages array as old
      setAllMessages((prevMessages) => {
        // Check if there are any messages and if the last message exists
        if (prevMessages.length > 0) {
          const lastMessage = prevMessages[prevMessages.length - 1];
          if (lastMessage) {
            lastMessage.New = false;
          }
        }
        return [...prevMessages];
      });
      
      // Mark the message as seen now that it's fully displayed
      if (user?.channelId && coachName) {
        markMessagesSeenByUser(user.channelId, coachName)
          .catch(err => console.error("Error marking message as seen:", err));
      }

      return;
    }

    // Render the next letter every 16ms
    const letterTimer = setTimeout(() => {
      setCurrentLetterIndex((prev) => Math.min(prev + 2, output.length));
    }, 6);

    return () => clearTimeout(letterTimer); // Cleanup timeout on unmount or updates
  }, [currentLetterIndex, outputArray]);



  // Update the subscription fetching effect
  useEffect(() => {
    const fetchUserData = async () => {
      if (user?.channelId) {
        try {
          const userData = await getUserData(user.channelId.toString());
          setUserData(userData);
        } catch (error) {
          console.error('Failed to fetch subscription:', error);
          setUserData({ planId: "free_plan" });
        }
      }
    };

    fetchUserData();
  }, [user?.channelId]);

  // Update the useEffect for notice checking
  useEffect(() => {
    const checkForNotices = async () => {
      try {
        // Pass the channelId to get proper LastNoticeSeenAt check
        const notice = await getLatestNotice(user.channelId);

        console.log("notice", notice);
        if (notice) {
          setLatestNotice(notice);
          if(!onboarded || user.firstMessage) {
            handleNoticeDismiss();
          }
        }
      } catch (error) {
        console.error('Failed to check notices:', error);
      }
    };

    // Only check for notices when the component mounts
    checkForNotices();
  }, []); // Empty dependency array - only run once on mount

  const handleNoticeDismiss = async () => {
    try {
      updateLastSeenNotice(user.channelId);
      setLatestNotice(null);
    } catch (error) {
      console.error('Failed to update last seen notice:', error);
    }
  };

  // Modify the useEffect for auto-focusing the goal input
  useEffect(() => {
    // If we're actively showing the goal input area
    const isMainViewActive = messageLoaded && !firstMessage && !loading;
    
    // Only auto-focus on desktop devices
    const isDesktop = !isMobileDevice();
    console.log("isDesktop", isDesktop);
    
    if (isMainViewActive && isDesktop) {
      // Use a slightly longer timeout to ensure everything has settled
      const focusTimer = setTimeout(() => {
        // Try direct DOM approach if ref is available
        if (goalInputRef.current) {
          // Force blur and refocus to trigger selection properly
          goalInputRef.current.blur();
          goalInputRef.current.focus();
          
          // Try various selection methods for cross-browser compatibility
          try {
            // Standard selection
            goalInputRef.current.select();
            
            // Explicit selection range
            goalInputRef.current.setSelectionRange(0, goalInputRef.current.value.length);
            
            // Force cursor to end (in case selection doesn't work)
            if (goalInputRef.current.value.length === 0) {
              // If empty, ensure cursor is at start for placeholder
              goalInputRef.current.setSelectionRange(0, 0);
            }
          } catch (e) {
            console.log("Selection error:", e);
          }
        }
      }, 300);
      
      return () => clearTimeout(focusTimer);
    }
  }, [messageLoaded, firstMessage, loading]);

  // Create the animations outside the render conditionals
  const onboardingSpringProps = useSpring({
    from: { opacity: 0 },
    to: { opacity: 1 },
    config: {
      ...config.gentle,
      duration: 500,
    },
  });

  // Fetch message usage when component mounts and after each message
  useEffect(() => {
    if (!user?.channelId) return;
    
    getUsage(user.channelId)
    .then(data => {
      if (data.userData) {
        setMessageUsage({
          count: data.userData.messageCountToday || 0,
          limit: data.plan.limits.messagesPerDay || 0
        });
      }
    })
    .catch(err => console.error("Error fetching message usage:", err));
  }, [user?.channelId, allMessages]);

  // Add useEffect to fetch enrollment message after component mounts
  useEffect(() => {
    // Only fetch if it wasn't already provided
    if (!initialEnrollmentMessage) {
      const fetchEnrollmentMessage = async () => {
        try {
          setIsLoadingEnrollment(true);
          // Call the server action directly
          const message = await generateEnrolmentMessage(user.channelId);
          setEnrollmentMessage(message);
        } catch (error) {
          console.error("Error fetching enrollment message:", error);
        } finally {
          setIsLoadingEnrollment(false);
        }
      };
      
      fetchEnrollmentMessage();
    }
  }, [initialEnrollmentMessage, user.channelId]);

  useEffect(() => {
    // Check if we're coming from a successful subscription
        // Get the plan type from URL parameters
    const params = new URLSearchParams(globalThis.location.search);

    if (params.get("subscription_success") === "true") {
      const SuccessPlanType = params.get('plan');
      if (!SuccessPlanType) return;
      
      const SuccessPlanTitle = SuccessPlanType === 'premium' ? 'Boundless' : 'Beyond';
      // Remove the query parameter without refreshing
      const newUrl = globalThis.location.pathname;
      globalThis.history.replaceState({}, document.title, newUrl);
      setSuccessPlanType(SuccessPlanType);
      setSuccessPlanTitle(SuccessPlanTitle);
      setShowSubscriptionSuccess(true);
    }
  }, []);

  // Add this near the beginning of the component
  useEffect(() => {
    // Use requestAnimationFrame to ensure DOM is ready
    requestAnimationFrame(() => {
      setIsInitialRender(false);
    });
  }, []);

  // Add new state for dropdown visibility
  const [dropdownVisible, setDropdownVisible] = useState(false);
  
  // Add transitions for components that used AnimatePresence
  const dropdownTransition = useTransition(menuOpen, {
    from: { opacity: 0, transform: 'translateY(-10px)' },
    enter: { opacity: 1, transform: 'translateY(0px)' },
    leave: { opacity: 0, transform: 'translateY(-10px)' },
    config: { 
      tension: 300, 
      friction: 20,
      // Shorter duration for UI elements
      duration: 200 
    }
  });
  
  // Add this near the top of your component where other state is defined
  const [isMobile, setIsMobile] = useState(false);

  // Add this useEffect to detect mobile devices
  useEffect(() => {
    // Check if we're on a mobile device
    const checkMobile = () => {
      const mobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
      );
      setIsMobile(mobile);
    };
    
    checkMobile();
    
    // Also check on resize in case of orientation changes
    globalThis.addEventListener('resize', checkMobile);
    return () => globalThis.removeEventListener('resize', checkMobile);
  }, []);

  const keepDrawer = (!isMobile || !chatVisible) && !showWelcome && !showFirstMessage;

  // Update the viewTransition to use more performant settings on mobile
  const viewTransition = useTransition(
    showWelcome ? "welcome" : 
    showFirstMessage ? "firstMessage" : "chat",
    {
      from: { opacity: 0 },
      enter: { opacity: 1 },
      leave: { opacity: 0 },
      // Use simpler animations on mobile
      config: isMobile 
        ? { 
            duration: 200,  // Shorter duration on mobile
            easing: t => t, // Linear easing (simpler)
          } 
        : { 
            tension: 280, 
            friction: 60,
            duration: 250 
          },
      // Skip initial animation
      immediate: isInitialRender,
      // Prevent multiple transitions from stacking
      exitBeforeEnter: true,
      // Use hardware acceleration
      trail: 0
    }
  );

  // Add this helper function to optimize animations
  const getOptimizedStyle = (baseStyle) => {
    // Add will-change and transform properties for hardware acceleration
    return {
      ...baseStyle,
      willChange: 'opacity, transform',
      WebkitBackfaceVisibility: 'hidden',
      backfaceVisibility: 'hidden',
      WebkitPerspective: 1000,
      perspective: 1000,
      WebkitTransformStyle: 'preserve-3d',
      transformStyle: 'preserve-3d'
    };
  };

  // Add state for success toast
  const [showSuccessToast, setShowSuccessToast] = useState(false);

  const [topUpSuccessToast, setTopUpSuccessToast] = useState<{ message: string, visible: boolean }>({ message: '', visible: false });
  
  // Add this effect to handle the top-up success parameter
  useEffect(() => {
    const handleTopUpSuccess = async () => {
      // Only run in browser environment to prevent hydration mismatch
      if (typeof window !== 'undefined') {
        const urlParams = new URLSearchParams(window.location.search);
        const isTopUpSuccess = urlParams.get('top_up_success') === 'true';
        const topUpId = urlParams.get('top_up_id');
        
        if (isTopUpSuccess && topUpId && userData) {
          // Find top-up amount from the ID
          const topUpOption = TOP_UP_OPTIONS.find(option => option.id === topUpId);
          if (topUpOption) {
            const totalEssence = userData.addedEssenceBalance + userData.monthlyEssenceBalance;
            // Format message with two lines - using <br/> for HTML line break
            const message = `${topUpOption.essence} Essence added!<br/>You now have ${totalEssence}.`;
            setTopUpSuccessToast({ message, visible: true });
            
            // Clean up the URL to prevent showing the toast again on refresh
            const newUrl = window.location.pathname;
            window.history.replaceState({}, document.title, newUrl);
          }
        }
      }
    };
    
    handleTopUpSuccess();
  }, [userData]);

  // Helper to check if the current user is a superuser
  // console.log('[SuperUser Check] Email:', user?.email, 'IsSuperUser:', isSuperUser);

  // Touch handlers for swipe gesture
  const handleTouchStart = (e: React.TouchEvent<HTMLDivElement>) => {
    // Only track if superuser to avoid unnecessary processing
    if (isSuperUser) { // <-- Update this check
      startXRef.current = e.touches[0].clientX;
    }
  };

  const handleTouchEnd = (e: React.TouchEvent<HTMLDivElement>) => {
    // Only process if superuser and touch started
    if (!isSuperUser || startXRef.current === null) return; // <-- Update this check

    const endX = e.changedTouches[0].clientX;
    const diff = startXRef.current - endX;
    const swipeThreshold = 50; // Minimum pixels to trigger swipe

    // Simple threshold check
    if (diff > swipeThreshold) {
      // Swiped left → next coach
      console.log("Swiped left");
      // cycleCoach(1);
    } else if (diff < -swipeThreshold) {
      // Swiped right → previous coach
      console.log("Swiped right");
      // cycleCoach(-1);
    }

    // Reset start position after swipe attempt
    startXRef.current = null;
  };

  // Handle coach selection
  const handleCoachSelect = async (newCoachName: string) => {
    // For mobile WhatsApp-like UI navigation
    if (isMobile) {
      setCoachName(newCoachName);
      setNavigationDirection('forward');
      setTimeout(() => setChatVisible(true), 100);
    } else {
      // For desktop, just update the coach
      setCoachName(newCoachName);
    }
  };

  // Add showCoachOverlay state near the other state variables (around line 570)
  const [showCoachOverlay, setShowCoachOverlay] = useState(false);

  // Add transition for chat view on mobile
  const chatTransition = useTransition(chatVisible, {
    from: { 
      opacity: 0, 
      transform: navigationDirection === 'forward' ? "translateX(100%)" : "translateX(-100%)" 
    },
    enter: { opacity: 1, transform: "translateX(0%)" },
    leave: { 
      opacity: 0, 
      transform: navigationDirection === 'forward' ? "translateX(-100%)" : "translateX(100%)" 
    },
    config: { tension: 300, friction: 30 },
  });

  // Handle chat visibility changes for proper cleanup
  useEffect(() => {
    if (!chatVisible && coachName && navigationDirection === 'backward') {
      // Chat visibility is being handled separately from coach selection
      // No need to reset coach selection here as it's persisted
    }
  }, [chatVisible, coachName, navigationDirection]);

  // ------------------------------
  // Menu swipe gesture (mobile)  
  // ------------------------------

  // Track swipe positions
  const touchStartXRef = useRef<number | null>(null);
  const touchEndXRef = useRef<number | null>(null);
  const touchStartYRef = useRef<number | null>(null); // Track vertical to differentiate scroll

  const SWIPE_THRESHOLD = 50;   // Min horizontal px to trigger
  const VERTICAL_THRESHOLD = 30; // Cancel if vertical scroll > this

  // Reference to main chat view (used elsewhere for measurements)
  const chatViewContainerRef = useRef<HTMLDivElement>(null);

  // Touch handlers for opening/closing drawer with swipe (WhatsApp-like)
  const handleMenuTouchStart = (e: React.TouchEvent<HTMLDivElement>) => {
    touchEndXRef.current = null; // reset
    touchStartXRef.current = e.targetTouches[0].clientX;
    touchStartYRef.current = e.targetTouches[0].clientY;
  };

  const handleMenuTouchMove = (e: React.TouchEvent<HTMLDivElement>) => {
    if (touchStartXRef.current === null) return; // not tracking

    touchEndXRef.current = e.targetTouches[0].clientX;

    const currentY = e.targetTouches[0].clientY;
    if (
      touchStartYRef.current !== null &&
      Math.abs(currentY - touchStartYRef.current) > VERTICAL_THRESHOLD
    ) {
      // Looks like vertical scrolling – cancel swipe tracking
      touchStartXRef.current = null;
      touchEndXRef.current = null;
      touchStartYRef.current = null;
    }
  };

  const handleMenuTouchEnd = () => {
    if (
      touchStartXRef.current === null ||
      touchEndXRef.current === null ||
      touchStartYRef.current === null
    ) {
      return;
    }

    const deltaX = touchEndXRef.current - touchStartXRef.current;

    // Swipe-right to open when menu is closed
    if (!menuOpen && deltaX > SWIPE_THRESHOLD) {
      setMenuOpen(true);
    }

    // reset refs
    touchStartXRef.current = null;
    touchEndXRef.current = null;
    touchStartYRef.current = null;
  };

  return (
    <Layout 
      user={user} 
      userData={userData} 
      enrollmentMessage={enrollmentMessage}
      isLoadingEnrollment={isLoadingEnrollment}
      currentPath="chat"
      keepDrawer={keepDrawer} // Pass the derived value
    >
      {showSubscriptionSuccess && (
        <div className="fixed inset-0 z-50 overflow-hidden">
          <SubscriptionSuccess 
            onComplete={() => setShowSubscriptionSuccess(false)} 
            planType={successPlanType}
            planTitle={successPlanTitle}
          />
        </div>
      )}

      
      
      {/* Fix the main container to properly constrain content */}
      <div className="relative flex flex-col min-h-svh max-h-svh bg-transparent overflow-hidden">
        {/* Payment Modal */}
        <PaymentOverlay 
          isOpen={isPaymentOpen} 
          onClose={() => setIsPaymentOpen(false)}
          user={user}
          userData={userData}
          enrollmentMessage={enrollmentMessage}
          isLoadingEnrollment={isLoadingEnrollment}
        />
        
        {/* Top-up Modal */}
        <TopUpOverlay
          isOpen={isTopUpOpen}
          onClose={() => setIsTopUpOpen(false)}
          channelId={user.channelId?.toString() || ''}
        />
        
        {latestNotice && (
          <NoticeOverlay
            notice={latestNotice}
            onDismiss={handleNoticeDismiss}
          />
        )}
        {/* NavDrawer removed - now part of Layout */}

        {/* Ensure the root component properly contains all content */}
        <div className="relative min-h-svh max-h-svh bg-transparent overflow-hidden root-component items-center touch-none component-a">
          {/* <ParticleField /> */}
          
          {/* Improve the view transition container */}
          <div className="absolute inset-0 w-full h-full">
            {viewTransition((style, item) => {
              // ... existing view transition code with the following changes:
              
              // For each view, ensure proper containment
              if (item === "welcome") {
                return (
                  <animated.div 
                    style={getOptimizedStyle({
                      ...style,
                      position: 'absolute',
                      width: '100%',
                      height: '100%'
                    })}
                    className="overflow-hidden"
                  >
                    <WelcomeMessageComponent
                      initiate={() => {
                        // Prevent multiple initiations 
                        if (isFirstMessageFetching) {
                          console.log("[FIRST_MESSAGE] Already initiating, ignoring duplicate call");
                          return;
                        }
                        
                        // Initialize audio context during user interaction to enable autoplay
                        initializeAudioContext();
                        
                        setShowWelcome(false);
                        setShowFirstMessage(true);
                        fetchFirstMessage(user.channelId);
                      }}
                    />
                  </animated.div>
                );
              } else if (item === "loading") {
                return (
                  <animated.div 
                    style={getOptimizedStyle({
                      ...style,
                      position: 'absolute',
                      width: '100%',
                      height: '100%'
                    })}
                    className="overflow-hidden flex items-center justify-center"
                  >
                    <Loader />
                  </animated.div>
                );
              } else if (item === "chat") {
                return (
                  <animated.div
                    ref={chatViewContainerRef} // Attach ref to the main chat view container
                    style={getOptimizedStyle({
                      ...style,
                      position: "absolute",
                      width: "100%",
                      height: "100%",
                    })}
                    className="relative flex min-h-dvh text-orange-300 component-b"
                    // Add touch handlers here
                    onTouchStart={handleMenuTouchStart}
                    onTouchMove={handleMenuTouchMove}
                    onTouchEnd={handleMenuTouchEnd}
                  >
                    {/* WhatsApp-like two-panel layout */}
                    {isMobile ? (
                      <div className="relative w-full h-full overflow-hidden">
                        {chatTransition((style, item) =>
                          item && coachName ? (
                            <animated.div
                              style={style}
                              className="absolute inset-0 w-full h-full"
                            >
                              <ChatMessage
                                input={input}
                                setInput={setInput}
                                isVoiceCall={isVoiceCall}
                                toggleVoiceCall={toggleVoiceCall}
                                sendMessage={sendMessage}
                                allMessages={allMessages}
                                setAllMessages={setAllMessages}
                                loading={loading}
                                setLoading={setLoading}
                                channelId={channelId}
                                reset={reset}
                                currentLetterIndex={currentLetterIndex}
                                messageRendering={messageRendering}
                                stopAudio={stopAudio}
                                onboarded={true}
                                setOnboarded={setOnboarded}
                                toggleStar={toggleStar}
                                onBack={() => {
                                  setNavigationDirection('backward');
                                  setChatVisible(false);
                                }}
                                fullscreenMode={false}
                                isMobile={isMobile}
                                loadMoreMessages={loadMoreMessages}
                                loadingMore={loadingMore}
                                hasMore={hasMore}
                                setHasMore={setHasMore}
                                messageCount={messageUsage.count}
                                dailyMessageLimit={messageUsage.limit}
                                isFreePlan={
                                  userData?.subscription?.planId === "free_plan"
                                }
                                coachName={coachName}
                                isMessageButtonGlowing={isMessageButtonGlowing}
                                setIsMessageButtonGlowing={
                                  setIsMessageButtonGlowing
                                }
                                availableCoaches={availableCoaches}
                                onCoachChange={setCoachName}
                                user={user}
                                /* cards props removed – handled inside ChatMessage */
                              />
                            </animated.div>
                          ) : (
                            <animated.div
                              style={style}
                              className="absolute inset-0 w-full h-full"
                            >
                              <CoachList
                                coaches={availableCoaches}
                                selectedCoach={null /* don't highlight any coach on mobile list */}
                                onCoachSelect={handleCoachSelect}
                                allMessages={allMessages}
                                user={user}
                                setAllMessages={setAllMessages}
                              />
                            </animated.div>
                          )
                        )}
                      </div>
                    ) : (
                      <div className="relative w-full h-full flex overflow-hidden">
                        <div
                          className={`w-full md:w-1/3 md:min-w-[300px] md:max-w-[400px] h-full border-r border-white/10`}
                        >
                          <CoachList
                            coaches={availableCoaches}
                            selectedCoach={coachName}
                            onCoachSelect={handleCoachSelect}
                            allMessages={allMessages}
                            user={user}
                            setAllMessages={setAllMessages}
                          />
                        </div>
                        <div
                          className={`h-full flex flex-col w-full md:flex-1`}
                        >
                          {coachName && (
                            <div className="w-full h-full">
                              <ChatMessage
                                input={input}
                                setInput={setInput}
                                isVoiceCall={isVoiceCall}
                                toggleVoiceCall={toggleVoiceCall}
                                sendMessage={sendMessage}
                                allMessages={allMessages}
                                setAllMessages={setAllMessages}
                                loading={loading}
                                setLoading={setLoading}
                                channelId={channelId}
                                reset={reset}
                                currentLetterIndex={currentLetterIndex}
                                messageRendering={messageRendering}
                                stopAudio={stopAudio}
                                onboarded={true}
                                setOnboarded={setOnboarded}
                                toggleStar={toggleStar}
                                onBack={() => {
                                  setNavigationDirection('backward');
                                  setChatVisible(false);
                                }}
                                fullscreenMode={false}
                                isMobile={isMobile}
                                loadMoreMessages={loadMoreMessages}
                                loadingMore={loadingMore}
                                hasMore={hasMore}
                                setHasMore={setHasMore}
                                messageCount={messageUsage.count}
                                dailyMessageLimit={messageUsage.limit}
                                isFreePlan={
                                  userData?.subscription?.planId === "free_plan"
                                }
                                coachName={coachName}
                                isMessageButtonGlowing={isMessageButtonGlowing}
                                setIsMessageButtonGlowing={
                                  setIsMessageButtonGlowing
                                }
                                availableCoaches={availableCoaches}
                                onCoachChange={setCoachName}
                                user={user}
                                /* cards props removed – handled inside ChatMessage */
                              />
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </animated.div>
                );
              } else if(item === "firstMessage") {
                return (
                  <FirstMessageComponent
                    onboarded={onboarded}
                    loading={loading}
                    reset={reset}
                    setOnboarded={setOnboarded}
                    setFirstMessage={setFirstMessage}
                    setShowFirstMessage={setShowFirstMessage}
                    setIsVoiceCall={setIsVoiceCall}
                    coachName={coachName}
                    originalOutput={originalOutput}
                    availableCoaches={availableCoaches}
                    setCoachName={setCoachName}
                    user={user}
                    audio={audio}
                    isPlaying={isPlaying}
                    rootAudioRef={rootAudioRef}
                    setIsPlaying={setIsPlaying}
                    resetOutputSentence={resetOutputSentence}
                    setOutput={setOutput}
                    setIsComplete={setIsComplete}
                    stopAudio={stopAudio}
                    audioScale={audioScale}
                    onboardingSpringProps={onboardingSpringProps}
                    setShowCoachOverlay={setShowCoachOverlay}
                    handleTouchStart={handleTouchStart}
                    handleTouchEnd={handleTouchEnd}
                  />
                )
              }
              return null;
            })}
          </div>

          {/* Voice Call Overlay */}
          {isVoiceCall && (
            // solid background
            <div className="absolute inset-0 z-50 bg-black component-b">
              <VoiceCall
                user={user}
                cardContext={voiceCallCard}
                coachName={coachName}
                onCallEnd={() => setIsVoiceCall(false)}
                onPaymentOpen={() => setIsPaymentOpen(true)}
                onTopUpOpen={() => setIsTopUpOpen(true)}
                setAllMessages={setAllMessages}
                audio={audio}
                rootAudioRef={rootAudioRef}
                setIsPlaying={setIsPlaying}
                resetOutputSentence={resetOutputSentence}
                setOutput={setOutput}
                originalOutput={originalOutput}
                setIsComplete={setIsComplete}
                stopAudio={stopAudio}
                audioScale={audioScale}
              />
            </div>
          )}
          
        </div>
      </div>

      {/* Add the CoachOverlay component to the end of the file but inside the main component */}
      {/* Add this right before the final component closing tag */}

      {/* Coach Details Overlay */}
      <CoachOverlay
        isVisible={showCoachOverlay}
        coachName={coachName}
        onClose={() => setShowCoachOverlay(false)}
        channelId={user?.channelId ?? ""}
      />
    </Layout>
  );
};