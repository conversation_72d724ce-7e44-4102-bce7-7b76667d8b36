"use client";

import React, { memo, useMemo } from "npm:react@canary";
import { format, isToday, isYesterday, isSameDay } from "npm:date-fns";
import { Button } from "@reframe/ui/main.tsx";
import { StarIcon } from "../../../../lib/icons.tsx";
import { DateSeparator } from "../../../../lib/icons.tsx";
import { MessageBubble } from "./MessageBubble.tsx";
import { AudioPlayer } from "../../../chat/chat-message/components/AudioPlayer.tsx";
import { getHumanCoachName, getCoachDisplayName } from "../../../chat/components/coach-utils.ts";

export type MessageType = {
  Id: string;
  Date?: string;
  Sender: string;
  Type?: string;
  Content: string;
  CoachName?: string;
  Audio?: string | Uint8Array;
  Status?: string;
  isTemporary?: boolean;
  New?: boolean;
};

interface Props {
  // When false, a `coach_message` is rendered like a normal bubble instead of a pill (used in CoachThread)
  userName?: string;
  showCoachPill?: boolean;
  // When true, adds orange border highlight to the message bubble
  highlight?: boolean;
  // When true, shows human coach name (removes " AI" suffix)
  showHumanCoachName?: boolean;
  message: MessageType;
  index: number;
  allMessages: MessageType[];
  contentReady: boolean;
  currentLetterIndex: number;
  activeAudioId: number;
  setActiveAudioId: (id: number) => void;
  toggleStar: (id: string, status: string) => void;
  onCoachMessageClick?: (message: MessageType) => void;
}

const MessageItemBase = ({ userName, message, index, allMessages, contentReady, currentLetterIndex, activeAudioId, setActiveAudioId, toggleStar, onCoachMessageClick, showCoachPill = true, highlight = false, showHumanCoachName = false }: Props) => {
  // Use useMemo for date calculations to avoid recalculating on every render
  const { isNewDay, dateLabel, isUser } = useMemo(() => {
    const msgDate = new Date(message.Date || Date.now());
    const prev = index > 0 ? allMessages[index - 1] : null;
    const prevDate = prev ? new Date(prev.Date || Date.now()) : null;
    const newDay = !prevDate || !isSameDay(msgDate, prevDate);
    let label = "";
    if (newDay) {
      if (isToday(msgDate)) label = "Today";
      else if (isYesterday(msgDate)) label = "Yesterday";
      else label = format(msgDate, "dd MMMM yyyy");
    }
    return {
      isNewDay: newDay,
      dateLabel: label,
      isUser: message.Sender === "user"
    };
  }, [message.Date, message.Sender, index, allMessages]);

  /* Render pill for direct coach messages */
  // console.log("message", message);
  
  if (message.Type === "coach_message" && showCoachPill) {
    if(message.Sender !== 'coach') {
      return null;
    }
    // Skip rendering if the previous message is also a coach message from the same coach
    // **on the same day**. If the date changes, we want a new pill so that a date separator
    // can be rendered before it.
    const prev = index > 0 ? allMessages[index - 1] : null;
    if (prev && prev.Type === "coach_message" && prev.Sender === 'coach') {
      const prevDate = prev.Date ? new Date(prev.Date) : null;
      const currDate = message.Date ? new Date(message.Date) : null;
      if (prevDate && currDate && isSameDay(prevDate, currDate)) {
        return null;
      }
    }

    // console.log("FILTERED message", message);
    return (
      <>
        {isNewDay && <DateSeparator date={dateLabel} />}
        <div data-id={message.Id} className="flex justify-center mb-6 w-full">
        <div className="relative">
          {/* Gradient glow effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-[#FF5727]/30 via-[#FCA311]/40 to-[#FF5727]/30 rounded-2xl blur-sm"></div>
          
          {/* Main button */}
          <Button
            variant="ghost"
            css="relative bg-gradient-to-r from-[#1a1a1a] to-[#2a2a2a] border border-[#FCA311]/50 hover:border-[#FCA311] text-[#FCA311] hover:text-white px-6 py-3 rounded-2xl backdrop-blur-sm shadow-lg hover:shadow-[0_8px_32px_rgba(252,163,17,0.25)] transition-all duration-300 ease-out transform hover:scale-[1.02] flex items-center gap-3 text-sm font-medium"
            onClick={() => onCoachMessageClick && onCoachMessageClick(message)}
          >
            {/* Message icon */}
            <div className="w-5 h-5 bg-gradient-to-br from-[#FCA311] to-[#FF5727] rounded-full flex items-center justify-center flex-shrink-0">
              <svg width="12" height="12" viewBox="0 0 24 24" fill="none" className="text-black">
                <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z" 
                      fill="currentColor"/>
              </svg>
            </div>
            
            {/* Text content */}
            <div className="flex flex-col items-start">
                <span className="text-xs opacity-80 leading-tight">User DM Thread</span>
            </div>
            
            {/* Arrow indicator */}
            <div className="w-4 h-4 opacity-60 group-hover:opacity-100 transition-opacity">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" className="text-current">
                <path d="M9 18l6-6-6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
          </Button>
          
          {/* Top accent line */}
          <div className="absolute top-0 left-4 right-4 h-[1px] bg-gradient-to-r from-transparent via-[#FCA311] to-transparent"></div>
        </div>
      </div>
      </>
    );
  }

  return (
    <>
      {isNewDay && <DateSeparator date={dateLabel} />}
      <div data-id={message.Id} className={`flex flex-col ${isUser ? 'items-start' : 'items-end'} mb-3`}>
        <div className="flex items-center mb-1 px-1">
          <span className="text-[#fcb645] text-[10px]">
            {isUser ? userName : "You"}
          </span>
          <span className="text-[#9B9B9B] text-[10px] ml-2">
            {message.Date ? format(new Date(message.Date), "h:mm a") : "Invalid time"}
          </span>
        </div>
        <div className={`flex items-start ${isUser ? 'justify-start' : 'justify-end'} w-full max-w-[90%]`}>
          {!isUser && !message.isTemporary && message.Id && !message.Id.startsWith('temp-') && (
            <Button variant="outline" css="w-7 h-7 p-1 rounded-full border-[#505050] hover:bg-black hover:border-[#505052] flex items-center justify-center self-center flex-shrink-0 mr-1" onClick={() => toggleStar(message.Id, message.Status || 'Default')}>
              <StarIcon className={`h-3.5 w-3.5 ${message.Status === 'Starred' ? 'text-[#FCA311] fill-[#FCA311]' : 'text-[#9B9B9B] fill-none'}`} />
            </Button>
          )}
          <MessageBubble isUser={isUser} contentReady={contentReady} highlight={highlight}>
            {message.Audio && (
              <AudioPlayer audio={message.Audio} index={index} activeAudioId={activeAudioId} setActiveAudioId={setActiveAudioId} />
            )}
            <p className="text-left text-base break-words whitespace-pre-wrap font-sans">
              {message.Sender === 'assistant' && index === allMessages.length - 1 && message.New
                ? message.Content.slice(0, currentLetterIndex)
                : message.Content}
            </p>
          </MessageBubble>
          {isUser && !message.isTemporary && message.Id && !message.Id.startsWith('temp-') && (
            <Button variant="outline" css="w-7 h-7 p-1 rounded-full border-[#505050] hover:bg-black hover:border-[#505052] flex items-center justify-center self-center flex-shrink-0 ml-1" onClick={() => toggleStar(message.Id, message.Status || 'Default')}>
              <StarIcon className={`h-3.5 w-3.5 ${message.Status === 'Starred' ? 'text-[#FCA311] fill-[#FCA311]' : 'text-[#9B9B9B] fill-none'}`} />
            </Button>
          )}
        </div>
      </div>
    </>
  );
};

// Export a memoized version of the component
export const MessageItem = memo(MessageItemBase, (prevProps, nextProps) => {
  // Return true if props are equal (no re-render needed)
  // Return false if props changed and re-render is needed
  return (
    prevProps.message.Id === nextProps.message.Id &&
    prevProps.message.Content === nextProps.message.Content &&
    prevProps.message.Status === nextProps.message.Status &&
    // Only check currentLetterIndex for the last message
    (prevProps.index !== nextProps.allMessages.length - 1 ||
      prevProps.currentLetterIndex === nextProps.currentLetterIndex) &&
    // Only check audio ID if this message has audio
    (prevProps.message.Audio ? prevProps.activeAudioId === nextProps.activeAudioId : true) &&
    prevProps.contentReady === nextProps.contentReady &&
    prevProps.showCoachPill === nextProps.showCoachPill &&
    prevProps.highlight === nextProps.highlight &&
    prevProps.showHumanCoachName === nextProps.showHumanCoachName &&
    prevProps.userName === nextProps.userName
  );
});
