import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';

// Configure how notifications are handled when the app is in the foreground
Notifications.setNotificationHandler({
  handleNotification: () => Promise.resolve({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

export interface PushToken {
  token: string;
  type: 'expo' | 'apns' | 'fcm';
}

export class NotificationService {
  private static instance: NotificationService;
  private pushToken: PushToken | null = null;

  private constructor() {}

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  /**
   * Request notification permissions and get push token
   */
  public async requestPermissionsAndGetToken(): Promise<PushToken | null> {
    try {
      // Check if device supports push notifications
      if (!Device.isDevice) {
        console.log('[PUSH_TOKEN] Push notifications only work on physical devices');
        return null;
      }

      // Request permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.log('[PUSH_TOKEN] Failed to get push token for push notification!');
        return null;
      }

      // Get the push token
      try {
        const tokenData = await Notifications.getExpoPushTokenAsync({
          projectId: 'b680fd01-b99d-4b40-aef0-5a5593d10145',
        });

        this.pushToken = {
          token: tokenData.data,
          type: 'expo'
        };

        console.log('[PUSH_TOKEN] Push token obtained:', this.pushToken.token);
        return this.pushToken;
      } catch (tokenError) {
        // Handle Expo Go limitations gracefully
        const errorString = String(tokenError);
        if (errorString.includes('VALIDATION_ERROR') ||
            errorString.includes('Invalid uuid') ||
            errorString.includes('EXPERIENCE_NOT_FOUND')) {
          console.warn('[PUSH_TOKEN] Push notifications not supported in Expo Go. Use a development build for full functionality.');
          console.warn('[PUSH_TOKEN] For testing, you can use the web test interface at /test-notifications');
          return null;
        }
        throw tokenError;
      }

    } catch (error) {
      console.error('[PUSH_TOKEN] Error getting push token:', error);
      return null;
    }
  }

  /**
   * Register push token with backend
   */
  public async registerTokenWithBackend(channelId: string, token: PushToken): Promise<boolean> {
    try {
      const baseUrl = 'https://awaken.is';
      
      const response = await fetch(`${baseUrl}/api/push-tokens/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          channelId,
          token: token.token,
          type: token.type,
          platform: Platform.OS,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to register token: ${response.status}`);
      }

      console.log('Push token registered successfully with backend');
      return true;
    } catch (error) {
      console.error('Error registering token with backend:', error);
      return false;
    }
  }

  /**
   * Initialize notification service - call this when user authenticates
   */
  public async initialize(channelId: string): Promise<void> {
    try {
      console.log(`[PUSH_TOKEN] Initializing notification service for channelId: ${channelId}`);
      const token = await this.requestPermissionsAndGetToken();
      if (token) {
        const success = await this.registerTokenWithBackend(channelId, token);
        if (success) {
          console.log(`[PUSH_TOKEN] Successfully initialized push notifications for channelId: ${channelId}`);
        } else {
          console.warn(`[PUSH_TOKEN] Failed to register token with backend for channelId: ${channelId}`);
        }
      } else {
        console.log(`[PUSH_TOKEN] No push token available for channelId: ${channelId} (this is normal in Expo Go)`);
      }
    } catch (error) {
      console.error('[PUSH_TOKEN] Error initializing notification service:', error);
    }
  }

  /**
   * Set up notification listeners
   */
  public setupNotificationListeners() {
    // Handle notification received while app is in foreground
    const foregroundSubscription = Notifications.addNotificationReceivedListener(notification => {
      console.log('Notification received in foreground:', notification);
    });

    // Handle notification response (user tapped notification)
    const responseSubscription = Notifications.addNotificationResponseReceivedListener(response => {
      console.log('Notification response:', response);
      
      // Handle navigation based on notification data
      const data = response.notification.request.content.data;
      if (data?.coachName) {
        // Navigate to specific coach conversation
        console.log('Navigate to coach:', data.coachName);
      }
    });

    return {
      foregroundSubscription,
      responseSubscription,
    };
  }

  /**
   * Get current push token
   */
  public getCurrentToken(): PushToken | null {
    return this.pushToken;
  }

  /**
   * Clear stored token (for logout)
   */
  public clearToken(): void {
    this.pushToken = null;
  }
}

export default NotificationService.getInstance();
