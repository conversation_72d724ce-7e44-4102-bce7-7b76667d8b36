import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';

// Configure how notifications are handled when the app is in the foreground
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

export interface PushToken {
  token: string;
  type: 'expo' | 'apns' | 'fcm';
}

export class NotificationService {
  private static instance: NotificationService;
  private pushToken: PushToken | null = null;

  private constructor() {}

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  /**
   * Request notification permissions and get push token
   */
  public async requestPermissionsAndGetToken(): Promise<PushToken | null> {
    try {
      // Check if device supports push notifications
      if (!Device.isDevice) {
        console.log('Push notifications only work on physical devices');
        return null;
      }

      // Request permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.log('Failed to get push token for push notification!');
        return null;
      }

      // Get the push token
      const tokenData = await Notifications.getExpoPushTokenAsync({
        projectId: process.env.EXPO_PUBLIC_PROJECT_ID || 'your-project-id',
      });

      this.pushToken = {
        token: tokenData.data,
        type: 'expo'
      };

      console.log('Push token obtained:', this.pushToken.token);
      return this.pushToken;

    } catch (error) {
      console.error('Error getting push token:', error);
      return null;
    }
  }

  /**
   * Register push token with backend
   */
  public async registerTokenWithBackend(channelId: string, token: PushToken): Promise<boolean> {
    try {
      const baseUrl = process.env.EXPO_PUBLIC_SOURCE_URI || 'https://awaken.is';
      
      const response = await fetch(`${baseUrl}/api/push-tokens/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          channelId,
          token: token.token,
          type: token.type,
          platform: Platform.OS,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to register token: ${response.status}`);
      }

      console.log('Push token registered successfully with backend');
      return true;
    } catch (error) {
      console.error('Error registering token with backend:', error);
      return false;
    }
  }

  /**
   * Initialize notification service - call this when user authenticates
   */
  public async initialize(channelId: string): Promise<void> {
    try {
      const token = await this.requestPermissionsAndGetToken();
      if (token) {
        await this.registerTokenWithBackend(channelId, token);
      }
    } catch (error) {
      console.error('Error initializing notification service:', error);
    }
  }

  /**
   * Set up notification listeners
   */
  public setupNotificationListeners() {
    // Handle notification received while app is in foreground
    const foregroundSubscription = Notifications.addNotificationReceivedListener(notification => {
      console.log('Notification received in foreground:', notification);
    });

    // Handle notification response (user tapped notification)
    const responseSubscription = Notifications.addNotificationResponseReceivedListener(response => {
      console.log('Notification response:', response);
      
      // Handle navigation based on notification data
      const data = response.notification.request.content.data;
      if (data?.coachName) {
        // Navigate to specific coach conversation
        console.log('Navigate to coach:', data.coachName);
      }
    });

    return {
      foregroundSubscription,
      responseSubscription,
    };
  }

  /**
   * Get current push token
   */
  public getCurrentToken(): PushToken | null {
    return this.pushToken;
  }

  /**
   * Clear stored token (for logout)
   */
  public clearToken(): void {
    this.pushToken = null;
  }
}

export default NotificationService.getInstance();
