import { Linking, Platform, StyleSheet } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { WebView } from "react-native-webview";
import React, { useRef } from "react";
import * as Localization from 'expo-localization';

// Get region information
const userRegion = Localization.region; // e.g., "US", "GB", "FR"
const userLocale = Localization.locale; // e.g., "en-US"
const userCurrency = Localization.currency; // e.g., "USD"

// JavaScript to inject into the WebView to enable Expo detection
const INJECTED_JAVASCRIPT = `
  // Mark this environment as Expo
  window.__EXPO_ENV__ = true;
  window.expo = { 
    isExpo: true,
    platform: '${Platform.OS}',
    region: '${userRegion}',
    locale: '${userLocale}',
    currency: '${userCurrency}',
  };
  
  // Add platform information (legacy support)
  window.__EXPO_PLATFORM__ = '${Platform.OS}';
  window.__EXPO_REGION__ = '${userRegion}';
  
  window.getExpoRegion = function() {
    return window.expo ? window.expo.region : null;
  };
  
  // Log for debugging
  console.log('[EXPO] Environment markers injected', {
    __EXPO_ENV__: window.__EXPO_ENV__,
    expo: window.expo,
    platform: window.__EXPO_PLATFORM__,
    region: window.__EXPO_REGION__,
    ReactNativeWebView: !!window.ReactNativeWebView
  });
  
  true; // Required for injectedJavaScript
`;

export default function HomeScreen() {
  const webViewRef = useRef(null);

  return (
    <SafeAreaView style={styles.container}>
      <WebView
        source={{ uri: process.env.EXPO_PUBLIC_SOURCE_URI || "https://awaken.is" }}
        ref={webViewRef}
        style={styles.webView}
        originWhitelist={["*"]}
        mediaPlaybackRequiresUserAction={false}
        domStorageEnabled
        javaScriptEnabled
        javaScriptCanOpenWindowsAutomatically
        mixedContentMode="always"
        allowsFullscreenVideo
        allowsInlineMediaPlaybook
        geolocationEnabled
        keyboardDisplayRequiresUserAction={false}
        pullToRefreshEnabled
        allowsBackForwardNavigationGestures
        injectedJavaScript={INJECTED_JAVASCRIPT}
        scalePageToFit={false}
        startInLoadingState={true}
        userAgent="Mozilla/5.0 (iPhone; CPU iPhone OS 17_7_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Mobile/15E148 Safari/604.1 ExpoWebView"
        scalesPageToFit={(Platform.OS === "ios") ? false : true}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000000",
  },
  webView: {
    flex: 1,
  },
});