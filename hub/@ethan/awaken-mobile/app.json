{"expo": {"name": "awaken-mobile", "slug": "awaken-mobile", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "awakenmobile", "userInterfaceStyle": "automatic", "newArchEnabled": true, "extra": {"eas": {"projectId": "b680fd01-b99d-4b40-aef0-5a5593d10145"}}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.bruteforcekid.awaken-mobile", "deploymentTarget": "13.0"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.bruteforcekid.awakenmobile"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-dev-client", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-localization", ["expo-notifications", {"icon": "./assets/images/notification-icon.png", "color": "#ffffff", "defaultChannel": "default"}]], "experiments": {"typedRoutes": true}}}